"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const dotenv_1 = __importDefault(require("dotenv"));
const supabaseClient_1 = require("./services/supabaseClient");
const pollingService_1 = require("./services/pollingService");
const healthService_1 = require("./services/healthService");
const logger_1 = require("./utils/logger");
dotenv_1.default.config();
class DataIngestionServer {
    constructor() {
        this.isShuttingDown = false;
    }
    async start() {
        try {
            logger_1.logger.info('🚀 Starting Pump.fun Data Ingestion Server...');
            this.validateEnvironment();
            const dbConnected = await (0, supabaseClient_1.getSupabaseService)().testConnection();
            if (!dbConnected) {
                throw new Error('Failed to connect to database');
            }
            this.setupShutdownHandlers();
            const healthPort = parseInt(process.env.HEALTH_PORT || '3005');
            healthService_1.healthService.start(healthPort);
            pollingService_1.pollingService.start();
            logger_1.logger.info('✅ Data Ingestion Server started successfully');
            logger_1.logger.info('📊 Polling jobs are now running...');
            const status = pollingService_1.pollingService.getStatus();
            Object.entries(status).forEach(([category, running]) => {
                logger_1.logger.info(`   ${category}: ${running ? '✅ Running' : '❌ Stopped'}`);
            });
        }
        catch (error) {
            logger_1.logger.error('❌ Failed to start server:', error);
            process.exit(1);
        }
    }
    validateEnvironment() {
        const required = ['SUPABASE_URL', 'SUPABASE_ANON_KEY'];
        const missing = required.filter(key => !process.env[key]);
        if (missing.length > 0) {
            throw new Error(`Missing required environment variables: ${missing.join(', ')}`);
        }
        logger_1.logger.info('✅ Environment variables validated');
    }
    setupShutdownHandlers() {
        const shutdown = async (signal) => {
            if (this.isShuttingDown) {
                logger_1.logger.warn('Force shutdown initiated');
                process.exit(1);
            }
            this.isShuttingDown = true;
            logger_1.logger.info(`🛑 Received ${signal}, shutting down gracefully...`);
            try {
                pollingService_1.pollingService.stop();
                healthService_1.healthService.stop();
                logger_1.logger.info('✅ Server shutdown completed');
                process.exit(0);
            }
            catch (error) {
                logger_1.logger.error('❌ Error during shutdown:', error);
                process.exit(1);
            }
        };
        process.on('SIGTERM', () => shutdown('SIGTERM'));
        process.on('SIGINT', () => shutdown('SIGINT'));
        process.on('SIGUSR2', () => shutdown('SIGUSR2'));
        process.on('uncaughtException', (error) => {
            logger_1.logger.error('💥 Uncaught Exception:', error);
            shutdown('uncaughtException');
        });
        process.on('unhandledRejection', (reason, promise) => {
            logger_1.logger.error('💥 Unhandled Rejection at:', promise, 'reason:', reason);
            shutdown('unhandledRejection');
        });
    }
}
const server = new DataIngestionServer();
server.start().catch((error) => {
    logger_1.logger.error('💥 Fatal error starting server:', error);
    process.exit(1);
});
//# sourceMappingURL=index.js.map