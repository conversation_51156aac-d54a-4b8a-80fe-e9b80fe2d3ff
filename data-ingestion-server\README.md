# Pump.fun Data Ingestion Server

A robust TypeScript Node.js server that continuously polls pump.fun API endpoints and maintains a synchronized Supabase PostgreSQL database with token data, categories, and milestone tracking.

## Features

- **Multi-API Support**: Integrates with Frontend API v3, Advanced API v2, and Legacy API
- **Smart Categorization**: Tracks tokens across 5 categories (For You, Featured, New, Graduated, Runners)
- **Dynamic Category Management**: Automatically adds/removes tokens from categories as they change
- **Milestone Tracking**: Records market cap milestones (2x, 3x, 5x, 10x, etc.)
- **Historical Data**: Maintains time-series market data for analytics
- **Robust Error Handling**: Retry logic, graceful failures, comprehensive logging
- **Configurable Polling**: Different intervals for each category (2-5 minutes)

## Architecture

```
src/
├── index.ts                 # Main entry point
├── types/
│   ├── unified.ts          # Unified data model types
│   └── api.ts              # API response types
├── services/
│   ├── supabaseClient.ts   # Database client
│   ├── apiService.ts       # HTTP API client
│   ├── pollingService.ts   # Cron job management
│   └── dataProcessor.ts    # Database operations
├── utils/
│   ├── logger.ts           # Logging utility
│   └── normalizer.ts       # Data transformation
└── config/
    └── constants.ts        # Configuration constants
```

## Prerequisites

- Node.js 18+
- Supabase project with the provided database schema
- Access to pump.fun APIs (optional: CORS proxy server)

## Setup

1. **Install dependencies:**
   ```bash
   npm install
   ```

2. **Configure environment:**
   ```bash
   cp .env.example .env
   ```
   
   Edit `.env` with your configuration:
   ```env
   SUPABASE_URL=https://your-project.supabase.co
   SUPABASE_ANON_KEY=your-anon-key-here
   USE_PROXY=false
   PROXY_BASE_URL=http://localhost:3003/api/proxy
   LOG_LEVEL=info
   ```

3. **Setup database schema:**
   Run the provided SQL schema in your Supabase SQL editor to create all required tables:
   ```sql
   -- Copy and paste the contents of database-schema.sql
   ```

   If you get RLS policy violations, run the fix script:
   ```sql
   -- Copy and paste the contents of fix-database-policies.sql
   ```

4. **Build the project:**
   ```bash
   npm run build
   ```

## Usage

### Development Mode
```bash
npm run dev
```

### Production Mode
```bash
npm start
```

### Watch Mode (auto-restart on changes)
```bash
npm run dev:watch
```

## Configuration

### Polling Intervals
Configure polling frequencies in `.env`:
```env
POLL_INTERVAL_FOR_YOU=5      # For You recommendations (minutes)
POLL_INTERVAL_FEATURED=5     # Featured tokens (minutes)
POLL_INTERVAL_NEW=2          # New tokens (minutes)
POLL_INTERVAL_GRADUATED=5    # Graduated tokens (minutes)
POLL_INTERVAL_RUNNERS=5      # Runners tokens (minutes)
```

### API Configuration
```env
API_LIMIT=50                 # Number of tokens to fetch per request
API_OFFSET=0                 # Starting offset for pagination
INCLUDE_NSFW=false           # Include NSFW tokens
```

### Proxy Configuration
If you need to use a CORS proxy:
```env
USE_PROXY=true
PROXY_BASE_URL=http://localhost:3003/api/proxy
```

## Database Schema

The server works with the following key tables:

- **tokens**: Core token information with initial and current market caps
- **categories**: Lookup table for token categories
- **token_categories**: Many-to-many relationship between tokens and categories
- **market_data_history**: Time-series market data
- **milestones**: Milestone definitions (2x, 3x, 5x, etc.)
- **achieved_milestones**: Records when tokens reach milestones

## Data Flow

1. **Polling**: Cron jobs fetch data from different pump.fun APIs
2. **Normalization**: Raw API responses are converted to unified format
3. **Validation**: Tokens are validated for required fields
4. **Database Operations**:
   - Upsert tokens (preserve initial market cap for new tokens)
   - Update category associations (remove stale, add new)
   - Insert market data history
   - Check and record new milestones

## Logging

The server provides comprehensive logging:

```
[2024-01-15T10:30:00.000Z] [INFO] [POLLING] for-you - Starting poll
[2024-01-15T10:30:01.000Z] [INFO] [API] GET https://frontend-api-v3.pump.fun/coins/for-you (200) {"dataLength":50}
[2024-01-15T10:30:02.000Z] [INFO] [POLLING] for-you - Fetched tokens {"count":50}
[2024-01-15T10:30:03.000Z] [INFO] [DATABASE] INSERT on tokens {"mint":"4v9NRnuBcLiR2VN3YHto1BQrstHxVJfc15WibVbUpump"}
```

## Error Handling

- **API Failures**: Automatic retry with exponential backoff
- **Database Errors**: Detailed logging and graceful failure handling
- **Data Validation**: Invalid tokens are filtered out with warnings
- **Graceful Shutdown**: Proper cleanup on SIGTERM/SIGINT

## Monitoring

Check server status through logs:
- Polling job status on startup
- Token counts per category
- Database operation results
- Error rates and retry attempts

## Troubleshooting

### Common Issues

1. **Database Connection Failed**
   - Verify SUPABASE_URL and SUPABASE_ANON_KEY
   - Check network connectivity
   - Ensure database schema is properly set up

2. **API Rate Limiting**
   - Increase polling intervals
   - Use proxy server if needed
   - Check API endpoint availability

3. **Memory Issues**
   - Monitor token batch sizes
   - Adjust API_LIMIT if needed
   - Check for memory leaks in long-running processes

### Debug Mode
Set `LOG_LEVEL=debug` for verbose logging:
```env
LOG_LEVEL=debug
```

## Contributing

1. Follow TypeScript best practices
2. Add comprehensive error handling
3. Include logging for debugging
4. Test with different API responses
5. Update documentation for new features

## License

MIT License - see LICENSE file for details
