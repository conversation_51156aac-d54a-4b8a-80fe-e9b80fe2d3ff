import { UnifiedToken, TokenHolder } from '../types/unified';
import { FrontendV3Token, AdvancedV2Token, LegacyToken, ApiType } from '../types/api';
import { logger } from './logger';

/**
 * Normalizes different API response formats into the unified token format
 */
export class TokenNormalizer {
  /**
   * Main normalization function that routes to specific normalizers based on API type
   */
  static normalizeToken(rawToken: any, apiType: ApiType): UnifiedToken {
    try {
      // Debug log to see raw token structure
      logger.debug('Normalizing token:', {
        apiType,
        tokenKeys: Object.keys(rawToken || {}),
        sampleData: {
          mint: rawToken?.mint || rawToken?.coinMint,
          name: rawToken?.name,
          symbol: rawToken?.symbol || rawToken?.ticker,
          coin: rawToken?.coin ? Object.keys(rawToken.coin) : undefined
        }
      });

      switch (apiType) {
        case ApiType.FRONTEND_V3:
          return this.normalizeFrontendV3Token(rawToken as FrontendV3Token);
        case ApiType.ADVANCED_V2:
          return this.normalizeAdvancedV2Token(rawToken);
        case ApiType.LEGACY:
          return this.normalizeLegacyToken(rawToken);
        default:
          throw new Error(`Unknown API type: ${apiType}`);
      }
    } catch (error) {
      logger.error('Token normalization failed:', {
        apiType,
        error: error instanceof Error ? error.message : error,
        tokenKeys: Object.keys(rawToken || {}),
        rawTokenSample: JSON.stringify(rawToken).substring(0, 500)
      });
      throw error;
    }
  }

  /**
   * Normalize Frontend API v3 token (For You)
   */
  private static normalizeFrontendV3Token(token: FrontendV3Token): UnifiedToken {
    return {
      // Core Identifiers - Frontend v3 uses standard field names
      mint: token.mint || '',
      name: token.name || '',
      symbol: token.symbol || '',
      creator: token.creator || '',

      // Core Metadata
      description: token.description || '',
      imageUrl: token.image_uri,
      metadataUrl: token.metadata_uri,
      websiteUrl: token.website,
      twitterUrl: token.twitter,
      telegramUrl: token.telegram,

      // Market & Financial Data
      marketCapUsd: token.usd_market_cap,
      marketCapSol: token.market_cap,
      volume24h: token.volume_24h,

      // Pump.fun Lifecycle Status
      creationTimestamp: token.created_timestamp,
      isComplete: token.complete,
      isLive: token.is_currently_live,

      // Pump.fun Specific IDs & Data
      bondingCurveAddress: token.bonding_curve,
      associatedBondingCurve: token.associated_bonding_curve,
      raydiumPoolAddress: token.raydium_pool,

      // Social & Status Flags
      isNsfw: token.nsfw || false,
      hasSocial: !!(token.website || token.twitter || token.telegram)
    };
  }

  /**
   * Normalize Advanced API v2 token (Featured, New, Graduated)
   */
  private static normalizeAdvancedV2Token(token: any): UnifiedToken {
    // Advanced API v2 may have different field structures, handle multiple possible field names
    return {
      // Core Identifiers - Handle both 'mint' and 'coinMint'
      mint: token.mint || token.coinMint || token.address || '',
      name: token.name || token.token_name || '',
      symbol: token.symbol || token.ticker || token.token_symbol || '',
      creator: token.creator || token.dev || token.developer || '',

      // Core Metadata
      description: token.description || '',
      imageUrl: token.image_uri || token.imageUrl || token.image,
      videoUrl: token.video_uri || token.videoUrl,
      bannerUrl: token.banner_uri || token.bannerUrl,
      metadataUrl: token.metadata_uri || token.metadataUrl,
      websiteUrl: token.website || token.website_url,
      twitterUrl: token.twitter || token.twitter_url,
      telegramUrl: token.telegram || token.telegram_url,

      // Market & Financial Data
      marketCapUsd: token.usd_market_cap || token.marketCapUsd,
      marketCapSol: token.market_cap || token.marketCap,
      volume24h: token.volume_24h || token.volume,
      totalSupply: token.total_supply || token.totalSupply,

      // Pump.fun Lifecycle Status
      creationTimestamp: token.created_timestamp || token.creationTime,
      graduationTimestamp: token.graduation_timestamp || token.graduationDate,
      isComplete: token.complete,
      isLive: token.is_currently_live,

      // Pump.fun Specific IDs & Data
      bondingCurveAddress: token.bonding_curve || token.bondingCurveAddress,
      associatedBondingCurve: token.associated_bonding_curve || token.associatedBondingCurve,
      raydiumPoolAddress: token.raydium_pool || token.poolAddress,
      virtualSolReserves: token.virtual_sol_reserves || token.virtualSolReserves,
      virtualTokenReserves: token.virtual_token_reserves || token.virtualTokenReserves,

      // Social & Status Flags
      replyCount: token.reply_count || token.replyCount,
      lastReplyTimestamp: token.last_reply || token.lastReplyTimestamp,
      isNsfw: token.nsfw || false,
      hasSocial: !!(token.website || token.twitter || token.telegram)
    };
  }

  /**
   * Normalize Legacy API token (Runners)
   */
  private static normalizeLegacyToken(token: any): UnifiedToken {
    // Legacy API has a nested structure with 'coin' object
    const coinData = token.coin || token;
    const holders: TokenHolder[] = token.holders?.map((holder: any) => ({
      holderId: holder.holderId,
      totalTokenAmountHeld: holder.totalTokenAmountHeld,
      ownedPercentage: holder.ownedPercentage,
      isSniper: holder.isSniper
    })) || [];

    return {
      // Core Identifiers - Legacy API uses 'coinMint' and nested 'coin' object
      mint: coinData.mint || coinData.coinMint || token.coinMint || '',
      name: coinData.name || token.name || '',
      symbol: coinData.symbol || coinData.ticker || token.ticker || '',
      creator: coinData.creator || coinData.dev || token.dev || '',

      // Core Metadata
      description: coinData.description || token.description || '',
      curatedDescription: token.curatedDescription || token.description,
      imageUrl: coinData.image_uri || coinData.imageUrl || token.imageUrl,
      videoUrl: coinData.video_uri || coinData.videoUrl || token.videoUrl,
      bannerUrl: coinData.banner_uri || coinData.bannerUrl || token.bannerUrl,
      metadataUrl: coinData.metadata_uri || coinData.metadataUrl || token.metadataUrl,
      websiteUrl: coinData.website || token.website,
      twitterUrl: coinData.twitter || token.twitter,
      telegramUrl: coinData.telegram || token.telegram,

      // Market & Financial Data
      marketCapSol: token.marketCap || coinData.market_cap,
      allTimeHighMarketCapSol: token.allTimeHighMarketCap,
      volume24h: token.volume || coinData.volume_24h,
      totalSupply: token.totalSupply || coinData.total_supply,
      currentMarketPrice: token.currentMarketPrice,

      // Transaction & Holder Analytics
      numHolders: token.holders?.length,
      topHoldersPercentage: token.topHoldersPercentage,
      devHoldingsPercentage: token.devHoldingsPercentage,
      buyTransactions: token.buyTransactions,
      sellTransactions: token.sellTransactions,
      totalTransactions: token.transactions,
      holders: holders,

      // Sniper Analytics
      sniperCount: token.sniperCount,
      sniperOwnedPercentage: token.sniperOwnedPercentage,

      // Pump.fun Lifecycle Status
      creationTimestamp: token.creationTime || coinData.created_timestamp,
      graduationTimestamp: token.graduationDate || coinData.graduation_timestamp,
      bondingCurveProgress: token.bondingCurveProgress,
      isComplete: token.complete || coinData.complete,

      // Pump.fun Specific IDs & Data
      raydiumPoolAddress: token.poolAddress || coinData.raydium_pool,
      virtualSolReserves: token.virtualSolReserves || coinData.virtual_sol_reserves,
      virtualTokenReserves: token.virtualTokenReserves || coinData.virtual_token_reserves,

      // Social & Status Flags
      replyCount: token.replyCount || coinData.reply_count,
      lastReplyTimestamp: token.lastReplyTimestamp || coinData.last_reply,
      hasSocial: !!(coinData.website || token.website || coinData.twitter || token.twitter || coinData.telegram || token.telegram),
      isNsfw: token.nsfw || coinData.nsfw || false,
      twitterReuseCount: token.twitterReuseCount
    };
  }

  /**
   * Validate that a normalized token has required fields
   */
  static validateToken(token: UnifiedToken): boolean {
    // Check for absolutely required fields
    if (!token.mint) {
      logger.warn('Token validation failed: missing mint address', {
        mint: token.mint,
        name: token.name,
        symbol: token.symbol,
        hasName: !!token.name,
        hasSymbol: !!token.symbol
      });
      return false;
    }

    // If name is missing, try to use symbol
    if (!token.name && token.symbol) {
      token.name = token.symbol;
      logger.debug('Using symbol as name for token', { mint: token.mint, symbol: token.symbol });
    }

    // If symbol is missing, try to use name
    if (!token.symbol && token.name) {
      token.symbol = token.name;
      logger.debug('Using name as symbol for token', { mint: token.mint, name: token.name });
    }

    // Final check - we need at least mint and either name or symbol
    if (!token.mint || (!token.name && !token.symbol)) {
      logger.warn('Token validation failed: missing critical fields', {
        mint: token.mint,
        name: token.name,
        symbol: token.symbol
      });
      return false;
    }

    return true;
  }
}

export const normalizeToken = TokenNormalizer.normalizeToken.bind(TokenNormalizer);
export const validateToken = TokenNormalizer.validateToken.bind(TokenNormalizer);
