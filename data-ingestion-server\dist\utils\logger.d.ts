export declare class Logger {
    private logLevel;
    constructor();
    private shouldLog;
    private formatMessage;
    error(message: string, ...args: any[]): void;
    warn(message: string, ...args: any[]): void;
    info(message: string, ...args: any[]): void;
    debug(message: string, ...args: any[]): void;
    polling(category: string, action: string, details?: any): void;
    database(operation: string, table: string, details?: any): void;
    api(method: string, url: string, status?: number, details?: any): void;
}
export declare const logger: Logger;
//# sourceMappingURL=logger.d.ts.map