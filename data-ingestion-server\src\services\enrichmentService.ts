import axios, { AxiosResponse } from 'axios';
import { RETRY_CONFIG } from '../config/constants';
import { logger } from '../utils/logger';

/**
 * DexScreener API response structure for token enrichment
 */
export interface DexScreenerTokenData {
  chainId: string;
  dexId: string;
  url: string;
  pairAddress: string;
  baseToken: {
    address: string;
    name: string;
    symbol: string;
  };
  priceNative: string;
  priceUsd: string;
  txns: {
    m5: { buys: number; sells: number };
    h1: { buys: number; sells: number };
    h6: { buys: number; sells: number };
    h24: { buys: number; sells: number };
  };
  volume: {
    h24: number;
  };
  priceChange: {
    m5: number;
    h1: number;
    h6: number;
    h24: number;
  };
  fdv: number;
  marketCap: number;
  pairCreatedAt: number;
}

/**
 * Enrichment data extracted from DexScreener
 */
export interface EnrichmentData {
  dexscreenerUrl: string;
  fdv: number;
  priceNative: number;
  priceUsd: number;
  priceChangeM5: number;
  priceChangeH1: number;
  priceChangeH6: number;
  priceChangeH24: number;
  graduationTimestamp: number;
  txnsM5Buys: number;
  txnsM5Sells: number;
  txnsH1Buys: number;
  txnsH1Sells: number;
  txnsH6Buys: number;
  txnsH6Sells: number;
  txnsH24Buys: number;
  txnsH24Sells: number;
}

/**
 * Service for enriching token data using DexScreener API
 */
export class EnrichmentService {
  private readonly baseUrl = 'https://api.dexscreener.com/latest/dex';

  /**
   * Enrich a single token with DexScreener data
   */
  async enrichToken(mint: string): Promise<EnrichmentData | null> {
    try {
      logger.debug(`Enriching token: ${mint}`);
      
      const url = `${this.baseUrl}/tokens/${mint}`;
      const response = await this.makeRequestWithRetry(url);
      
      if (!response?.data?.pairs || response.data.pairs.length === 0) {
        logger.debug(`No DexScreener data found for token: ${mint}`);
        return null;
      }

      // Find the best pair (prefer pumpfun, then by highest volume)
      const pairs = response.data.pairs;
      const pumpfunPair = pairs.find((pair: any) => pair.dexId === 'pumpfun');
      const bestPair = pumpfunPair || pairs.sort((a: any, b: any) => (b.volume?.h24 || 0) - (a.volume?.h24 || 0))[0];

      if (!bestPair) {
        logger.debug(`No suitable pair found for token: ${mint}`);
        return null;
      }

      const enrichmentData: EnrichmentData = {
        dexscreenerUrl: bestPair.url || '',
        fdv: parseFloat(bestPair.fdv) || 0,
        priceNative: parseFloat(bestPair.priceNative) || 0,
        priceUsd: parseFloat(bestPair.priceUsd) || 0,
        priceChangeM5: bestPair.priceChange?.m5 || 0,
        priceChangeH1: bestPair.priceChange?.h1 || 0,
        priceChangeH6: bestPair.priceChange?.h6 || 0,
        priceChangeH24: bestPair.priceChange?.h24 || 0,
        graduationTimestamp: bestPair.pairCreatedAt || 0,
        txnsM5Buys: bestPair.txns?.m5?.buys || 0,
        txnsM5Sells: bestPair.txns?.m5?.sells || 0,
        txnsH1Buys: bestPair.txns?.h1?.buys || 0,
        txnsH1Sells: bestPair.txns?.h1?.sells || 0,
        txnsH6Buys: bestPair.txns?.h6?.buys || 0,
        txnsH6Sells: bestPair.txns?.h6?.sells || 0,
        txnsH24Buys: bestPair.txns?.h24?.buys || 0,
        txnsH24Sells: bestPair.txns?.h24?.sells || 0,
      };

      logger.debug(`Successfully enriched token: ${mint}`, {
        dexscreenerUrl: enrichmentData.dexscreenerUrl,
        fdv: enrichmentData.fdv,
        priceUsd: enrichmentData.priceUsd
      });

      return enrichmentData;

    } catch (error) {
      if (axios.isAxiosError(error)) {
        if (error.response?.status === 404) {
          logger.debug(`Token not found on DexScreener: ${mint}`);
          return null;
        }
        if (error.response?.status && error.response.status >= 500) {
          logger.warn(`DexScreener server error for token ${mint}:`, error.response.status);
          return null;
        }
      }
      
      logger.error(`Failed to enrich token ${mint}:`, error);
      return null;
    }
  }

  /**
   * Enrich multiple tokens in batch
   */
  async enrichTokens(mints: string[]): Promise<Map<string, EnrichmentData>> {
    const enrichmentMap = new Map<string, EnrichmentData>();
    
    logger.debug(`Enriching ${mints.length} tokens`);
    
    // Process tokens in parallel with some concurrency control
    const batchSize = 5; // Limit concurrent requests to avoid rate limiting
    for (let i = 0; i < mints.length; i += batchSize) {
      const batch = mints.slice(i, i + batchSize);
      const promises = batch.map(async (mint) => {
        const enrichmentData = await this.enrichToken(mint);
        if (enrichmentData) {
          enrichmentMap.set(mint, enrichmentData);
        }
      });
      
      await Promise.all(promises);
      
      // Small delay between batches to be respectful to the API
      if (i + batchSize < mints.length) {
        await new Promise(resolve => setTimeout(resolve, 100));
      }
    }
    
    logger.debug(`Successfully enriched ${enrichmentMap.size} out of ${mints.length} tokens`);
    return enrichmentMap;
  }

  /**
   * Make HTTP request with retry logic
   */
  private async makeRequestWithRetry(url: string): Promise<AxiosResponse | null> {
    let lastError: any;
    
    for (let attempt = 0; attempt <= RETRY_CONFIG.MAX_RETRIES; attempt++) {
      try {
        const response = await axios.get(url, {
          timeout: 10000, // 10 second timeout
          headers: {
            'User-Agent': 'pump-fun-data-ingestion/1.0'
          }
        });
        
        return response;
      } catch (error) {
        lastError = error;
        
        if (axios.isAxiosError(error)) {
          // Don't retry on 4xx errors (except 429)
          if (error.response?.status && error.response.status >= 400 && error.response.status < 500 && error.response.status !== 429) {
            throw error;
          }
        }
        
        if (attempt < RETRY_CONFIG.MAX_RETRIES) {
          const delay = RETRY_CONFIG.RETRY_DELAY * Math.pow(RETRY_CONFIG.BACKOFF_MULTIPLIER, attempt);
          logger.debug(`Retrying DexScreener request in ${delay}ms (attempt ${attempt + 1}/${RETRY_CONFIG.MAX_RETRIES + 1})`);
          await new Promise(resolve => setTimeout(resolve, delay));
        }
      }
    }
    
    throw lastError;
  }
}

export const enrichmentService = new EnrichmentService();
