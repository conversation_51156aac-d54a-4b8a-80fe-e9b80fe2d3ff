const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

async function testConnection() {
  console.log('?? Testing Supabase connection...');
  console.log('URL:', process.env.SUPABASE_URL);
  console.log('Key:', process.env.SUPABASE_ANON_KEY ? 'Present' : 'Missing');
  
  const supabase = createClient(
    process.env.SUPABASE_URL,
    process.env.SUPABASE_ANON_KEY
  );
  
  try {
    // Test connection by listing tables
    const { data, error } = await supabase
      .from('tokens')
      .select('count', { count: 'exact', head: true });
    
    if (error) {
      console.error('? Connection failed:', error.message);
      return false;
    }
    
    console.log('? Connection successful!');
    console.log('?? Tables accessible from Oracle Supabase');
    return true;
  } catch (err) {
    console.error('? Connection error:', err.message);
    return false;
  }
}

testConnection().then(success => {
  process.exit(success ? 0 : 1);
});
