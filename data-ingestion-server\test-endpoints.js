#!/usr/bin/env node

/**
 * Quick test script to verify which pump.fun endpoints are working
 * Run with: node test-endpoints.js
 */

const axios = require('axios');

const endpoints = [
  {
    name: 'For You (Frontend v3)',
    url: 'https://frontend-api-v3.pump.fun/coins/for-you?offset=0&limit=5&includeNsfw=false'
  },
  {
    name: 'Featured (Advanced v2)',
    url: 'https://advanced-api-v2.pump.fun/coins/list?sortBy=featured&limit=5&offset=0'
  },
  {
    name: 'New (Advanced v2)',
    url: 'https://advanced-api-v2.pump.fun/coins/list?limit=5&offset=0'
  },
  {
    name: 'Graduated (Advanced v2)',
    url: 'https://advanced-api-v2.pump.fun/coins/graduated?limit=5&offset=0'
  },
  {
    name: 'Runners (Legacy)',
    url: 'https://pump.fun/api/runners'
  }
];

async function testEndpoint(endpoint) {
  try {
    console.log(`\n🔍 Testing: ${endpoint.name}`);
    console.log(`   URL: ${endpoint.url}`);
    
    const response = await axios.get(endpoint.url, {
      timeout: 10000,
      headers: {
        'Accept': 'application/json',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
      }
    });
    
    const data = response.data;
    const isArray = Array.isArray(data);
    const count = isArray ? data.length : 'N/A';
    const sampleKeys = isArray && data.length > 0 ? Object.keys(data[0]).slice(0, 5) : [];
    
    console.log(`   ✅ Status: ${response.status}`);
    console.log(`   📊 Type: ${typeof data} (${isArray ? 'array' : 'object'})`);
    console.log(`   🔢 Count: ${count}`);
    if (sampleKeys.length > 0) {
      console.log(`   🔑 Sample keys: ${sampleKeys.join(', ')}`);
    }
    
    return { success: true, count, status: response.status };
    
  } catch (error) {
    console.log(`   ❌ Error: ${error.response?.status || 'Network'} - ${error.message}`);
    if (error.response?.data) {
      console.log(`   📝 Response: ${JSON.stringify(error.response.data).substring(0, 200)}`);
    }
    
    return { success: false, error: error.message, status: error.response?.status };
  }
}

async function testAllEndpoints() {
  console.log('🚀 Testing Pump.fun API Endpoints...\n');
  
  const results = {};
  
  for (const endpoint of endpoints) {
    const result = await testEndpoint(endpoint);
    results[endpoint.name] = result;
    
    // Wait a bit between requests
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
  
  console.log('\n📋 Summary:');
  console.log('=' .repeat(50));
  
  Object.entries(results).forEach(([name, result]) => {
    const status = result.success ? '✅' : '❌';
    const info = result.success ? 
      `${result.count} tokens (${result.status})` : 
      `${result.status || 'Network'} error`;
    console.log(`${status} ${name}: ${info}`);
  });
  
  const workingCount = Object.values(results).filter(r => r.success).length;
  console.log(`\n🎯 Working endpoints: ${workingCount}/${endpoints.length}`);
  
  if (workingCount < endpoints.length) {
    console.log('\n💡 Suggestions:');
    console.log('   - Try using the CORS proxy server (USE_PROXY=true)');
    console.log('   - Check if pump.fun APIs are temporarily down');
    console.log('   - Verify endpoint URLs are correct');
  }
}

testAllEndpoints().catch(console.error);
