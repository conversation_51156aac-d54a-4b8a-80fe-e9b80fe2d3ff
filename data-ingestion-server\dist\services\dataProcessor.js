"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.dataProcessor = exports.DataProcessor = void 0;
const supabaseClient_1 = require("./supabaseClient");
const constants_1 = require("../config/constants");
const logger_1 = require("../utils/logger");
class DataProcessor {
    async processAndUpsertData(tokens, categorySlug) {
        if (tokens.length === 0) {
            logger_1.logger.warn(`No tokens to process for category: ${categorySlug}`);
            return;
        }
        logger_1.logger.polling(categorySlug, 'Processing tokens', { count: tokens.length });
        try {
            const category = await this.getCategoryBySlug(categorySlug);
            if (!category) {
                throw new Error(`Category not found: ${categorySlug}`);
            }
            for (const token of tokens) {
                await this.processToken(token, category.id);
            }
            await this.updateCategoryAssociations(tokens.map(t => t.mint), category.id);
            logger_1.logger.polling(categorySlug, 'Successfully processed tokens', { count: tokens.length });
        }
        catch (error) {
            logger_1.logger.error(`Failed to process tokens for category ${categorySlug}:`, {
                error: error instanceof Error ? error.message : error,
                stack: error instanceof Error ? error.stack : undefined,
                categorySlug,
                tokenCount: tokens.length
            });
            throw error;
        }
    }
    async processToken(token, categoryId) {
        try {
            const dbToken = this.convertToDbFormat(token);
            await this.upsertToken(dbToken);
            if (token.marketCapUsd || token.marketCapSol || token.volume24h) {
                await this.addMarketDataHistory(token);
            }
            if (token.marketCapUsd) {
                await this.checkAndRecordMilestones(token.mint, token.marketCapUsd);
            }
        }
        catch (error) {
            logger_1.logger.error(`Failed to process token ${token.mint}:`, error);
            throw error;
        }
    }
    async upsertToken(token) {
        try {
            const { data: existingToken } = await (0, supabaseClient_1.getSupabase)()
                .from(constants_1.DB_TABLES.TOKENS)
                .select('mint, initial_market_cap_usd')
                .eq('mint', token.mint)
                .single();
            if (existingToken) {
                const updateData = {
                    ...token,
                    updated_at: new Date().toISOString()
                };
                delete updateData.mint;
                delete updateData.initial_market_cap_usd;
                delete updateData.created_at;
                const { error } = await (0, supabaseClient_1.getSupabase)()
                    .from(constants_1.DB_TABLES.TOKENS)
                    .update(updateData)
                    .eq('mint', token.mint);
                if (error)
                    throw error;
                logger_1.logger.database('UPDATE', constants_1.DB_TABLES.TOKENS, { mint: token.mint });
            }
            else {
                const insertData = {
                    ...token,
                    initial_market_cap_usd: token.current_market_cap_usd || 0,
                    created_at: new Date().toISOString(),
                    updated_at: new Date().toISOString()
                };
                const { error } = await (0, supabaseClient_1.getSupabase)()
                    .from(constants_1.DB_TABLES.TOKENS)
                    .insert(insertData);
                if (error)
                    throw error;
                logger_1.logger.database('INSERT', constants_1.DB_TABLES.TOKENS, { mint: token.mint });
            }
        }
        catch (error) {
            logger_1.logger.error(`Failed to upsert token ${token.mint}:`, error);
            throw error;
        }
    }
    async addMarketDataHistory(token) {
        try {
            const historyData = {
                timestamp: new Date().toISOString(),
                token_mint: token.mint,
                market_cap_usd: token.marketCapUsd,
                market_cap_sol: token.marketCapSol,
                volume_24h: token.volume24h,
                num_holders: token.numHolders
            };
            const enrichmentTxns = token.enrichmentTxns;
            if (enrichmentTxns) {
                historyData.txns_m5_buys = enrichmentTxns.txnsM5Buys;
                historyData.txns_m5_sells = enrichmentTxns.txnsM5Sells;
                historyData.txns_h1_buys = enrichmentTxns.txnsH1Buys;
                historyData.txns_h1_sells = enrichmentTxns.txnsH1Sells;
                historyData.txns_h6_buys = enrichmentTxns.txnsH6Buys;
                historyData.txns_h6_sells = enrichmentTxns.txnsH6Sells;
                historyData.txns_h24_buys = enrichmentTxns.txnsH24Buys;
                historyData.txns_h24_sells = enrichmentTxns.txnsH24Sells;
            }
            const { error } = await (0, supabaseClient_1.getSupabase)()
                .from(constants_1.DB_TABLES.MARKET_DATA_HISTORY)
                .insert(historyData);
            if (error)
                throw error;
            logger_1.logger.database('INSERT', constants_1.DB_TABLES.MARKET_DATA_HISTORY, { mint: token.mint });
        }
        catch (error) {
            logger_1.logger.error(`Failed to add market data history for ${token.mint}:`, error);
            throw error;
        }
    }
    async checkAndRecordMilestones(mint, currentMarketCapUsd) {
        try {
            const { data: tokenData } = await (0, supabaseClient_1.getSupabase)()
                .from(constants_1.DB_TABLES.TOKENS)
                .select('initial_market_cap_usd')
                .eq('mint', mint)
                .single();
            if (!tokenData?.initial_market_cap_usd || tokenData.initial_market_cap_usd === 0) {
                return;
            }
            const multiplier = currentMarketCapUsd / tokenData.initial_market_cap_usd;
            const { data: milestones } = await (0, supabaseClient_1.getSupabase)()
                .from(constants_1.DB_TABLES.MILESTONES)
                .select('*')
                .lte('multiplier', multiplier);
            if (!milestones || milestones.length === 0)
                return;
            const { data: achievedMilestones } = await (0, supabaseClient_1.getSupabase)()
                .from(constants_1.DB_TABLES.ACHIEVED_MILESTONES)
                .select('milestone_id')
                .eq('token_mint', mint);
            const achievedMilestoneIds = new Set(achievedMilestones?.map((am) => am.milestone_id) || []);
            const newMilestones = milestones.filter((m) => !achievedMilestoneIds.has(m.id));
            if (newMilestones.length > 0) {
                const achievementData = newMilestones.map((milestone) => ({
                    token_mint: mint,
                    milestone_id: milestone.id,
                    achieved_at_timestamp: new Date().toISOString(),
                    market_cap_at_milestone: currentMarketCapUsd
                }));
                const { error } = await (0, supabaseClient_1.getSupabase)()
                    .from(constants_1.DB_TABLES.ACHIEVED_MILESTONES)
                    .insert(achievementData);
                if (error)
                    throw error;
                logger_1.logger.database('INSERT', constants_1.DB_TABLES.ACHIEVED_MILESTONES, {
                    mint,
                    newMilestones: newMilestones.map((m) => `${m.multiplier}x`)
                });
            }
        }
        catch (error) {
            logger_1.logger.error(`Failed to check milestones for ${mint}:`, error);
            throw error;
        }
    }
    async updateCategoryAssociations(tokenMints, categoryId) {
        try {
            logger_1.logger.debug('Updating category associations', {
                categoryId,
                tokenCount: tokenMints.length,
                sampleMints: tokenMints.slice(0, 3)
            });
            const { error: deleteError } = await (0, supabaseClient_1.getSupabase)()
                .from(constants_1.DB_TABLES.TOKEN_CATEGORIES)
                .delete()
                .eq('category_id', categoryId);
            if (deleteError) {
                logger_1.logger.error('Failed to delete existing category associations:', deleteError);
                throw deleteError;
            }
            const categoryData = tokenMints.map(mint => ({
                token_mint: mint,
                category_id: categoryId,
                created_at: new Date().toISOString()
            }));
            const { error: upsertError } = await (0, supabaseClient_1.getSupabase)()
                .from(constants_1.DB_TABLES.TOKEN_CATEGORIES)
                .insert(categoryData);
            if (upsertError) {
                logger_1.logger.error('Failed to upsert category associations:', {
                    error: upsertError,
                    categoryId,
                    sampleData: categoryData.slice(0, 3)
                });
                throw upsertError;
            }
            logger_1.logger.database('UPSERT', constants_1.DB_TABLES.TOKEN_CATEGORIES, {
                categoryId,
                tokenCount: tokenMints.length
            });
        }
        catch (error) {
            logger_1.logger.error(`Failed to update category associations for category ${categoryId}:`, {
                error: error instanceof Error ? error.message : error,
                stack: error instanceof Error ? error.stack : undefined,
                categoryId,
                tokenCount: tokenMints.length,
                sampleMints: tokenMints.slice(0, 3)
            });
            throw error;
        }
    }
    async getCategoryBySlug(slug) {
        try {
            const { data, error } = await (0, supabaseClient_1.getSupabase)()
                .from(constants_1.DB_TABLES.CATEGORIES)
                .select('*')
                .eq('slug', slug)
                .single();
            if (error) {
                if (error.code === 'PGRST116') {
                    return null;
                }
                throw error;
            }
            return data;
        }
        catch (error) {
            logger_1.logger.error(`Failed to get category by slug ${slug}:`, error);
            throw error;
        }
    }
    convertToDbFormat(token) {
        const dbToken = {
            mint: token.mint,
            name: token.name,
            symbol: token.symbol,
            creator: token.creator,
            ...(token.description !== undefined && { description: token.description }),
            ...(token.curatedDescription !== undefined && { curated_description: token.curatedDescription }),
            ...(token.imageUrl !== undefined && { image_url: token.imageUrl }),
            ...(token.videoUrl !== undefined && { video_url: token.videoUrl }),
            ...(token.bannerUrl !== undefined && { banner_url: token.bannerUrl }),
            ...(token.websiteUrl !== undefined && { website_url: token.websiteUrl }),
            ...(token.twitterUrl !== undefined && { twitter_url: token.twitterUrl }),
            ...(token.telegramUrl !== undefined && { telegram_url: token.telegramUrl }),
            ...(token.metadataUrl !== undefined && { metadata_url: token.metadataUrl }),
            ...(token.dexscreenerUrl !== undefined && { dexscreener_url: token.dexscreenerUrl }),
            ...(token.marketCapUsd !== undefined && { current_market_cap_usd: token.marketCapUsd }),
            ...(token.fdv !== undefined && { fdv: token.fdv }),
            ...(token.priceUsd !== undefined && { price_usd: token.priceUsd }),
            ...(token.priceNative !== undefined && { price_native: token.priceNative }),
            ...(token.priceChangeM5 !== undefined && { price_change_m5: token.priceChangeM5 }),
            ...(token.priceChangeH1 !== undefined && { price_change_h1: token.priceChangeH1 }),
            ...(token.priceChangeH6 !== undefined && { price_change_h6: token.priceChangeH6 }),
            ...(token.priceChangeH24 !== undefined && { price_change_h24: token.priceChangeH24 }),
            ...(token.bondingCurveAddress !== undefined && { bonding_curve_address: token.bondingCurveAddress }),
            ...(token.associatedBondingCurve !== undefined && { associated_bonding_curve: token.associatedBondingCurve }),
            ...(token.raydiumPoolAddress !== undefined && { raydium_pool_address: token.raydiumPoolAddress }),
            ...(token.graduationTimestamp !== undefined && {
                graduation_timestamp: new Date(token.graduationTimestamp).toISOString()
            }),
            ...(token.isNsfw !== undefined && { is_nsfw: token.isNsfw }),
            ...(token.isBanned !== undefined && { is_banned: token.isBanned }),
            initial_market_cap_usd: 0
        };
        return dbToken;
    }
}
exports.DataProcessor = DataProcessor;
exports.dataProcessor = new DataProcessor();
//# sourceMappingURL=dataProcessor.js.map