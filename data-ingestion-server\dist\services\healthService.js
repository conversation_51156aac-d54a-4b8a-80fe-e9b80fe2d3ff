"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.healthService = exports.HealthService = void 0;
const express_1 = __importDefault(require("express"));
const supabaseClient_1 = require("./supabaseClient");
const pollingService_1 = require("./pollingService");
const logger_1 = require("../utils/logger");
class HealthService {
    constructor() {
        this.app = (0, express_1.default)();
        this.setupRoutes();
    }
    setupRoutes() {
        this.app.get('/health', async (req, res) => {
            try {
                const health = await this.getHealthStatus();
                const statusCode = health.status === 'healthy' ? 200 : 503;
                res.status(statusCode).json(health);
            }
            catch (error) {
                logger_1.logger.error('Health check failed:', error);
                res.status(503).json({
                    status: 'unhealthy',
                    error: 'Health check failed'
                });
            }
        });
        this.app.get('/status', (req, res) => {
            const pollingStatus = pollingService_1.pollingService.getStatus();
            res.json({
                status: 'running',
                polling: pollingStatus,
                uptime: process.uptime(),
                memory: process.memoryUsage(),
                timestamp: new Date().toISOString()
            });
        });
    }
    async getHealthStatus() {
        const dbHealthy = await (0, supabaseClient_1.getSupabaseService)().testConnection();
        const pollingStatus = pollingService_1.pollingService.getStatus();
        const pollingHealthy = Object.values(pollingStatus).some(running => running);
        const status = dbHealthy && pollingHealthy ? 'healthy' : 'unhealthy';
        return {
            status,
            timestamp: new Date().toISOString(),
            uptime: process.uptime(),
            database: dbHealthy ? 'connected' : 'disconnected',
            polling: {
                status: pollingHealthy ? 'running' : 'stopped',
                jobs: pollingStatus
            },
            memory: process.memoryUsage()
        };
    }
    start(port = 3005) {
        this.server = this.app.listen(port, () => {
            logger_1.logger.info(`Health service running on port ${port}`);
            logger_1.logger.info(`Health check: http://localhost:${port}/health`);
            logger_1.logger.info(`Status endpoint: http://localhost:${port}/status`);
        });
    }
    stop() {
        if (this.server) {
            this.server.close();
            logger_1.logger.info('Health service stopped');
        }
    }
}
exports.HealthService = HealthService;
exports.healthService = new HealthService();
//# sourceMappingURL=healthService.js.map