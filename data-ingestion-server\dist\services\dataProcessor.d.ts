import { UnifiedToken } from '../types/unified';
export declare class DataProcessor {
    processAndUpsertData(tokens: UnifiedToken[], categorySlug: string): Promise<void>;
    private processToken;
    private upsertToken;
    private addMarketDataHistory;
    private checkAndRecordMilestones;
    private updateCategoryAssociations;
    private getCategoryBySlug;
    private convertToDbFormat;
}
export declare const dataProcessor: DataProcessor;
//# sourceMappingURL=dataProcessor.d.ts.map