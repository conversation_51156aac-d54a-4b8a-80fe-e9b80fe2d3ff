{"version": 3, "file": "constants.js", "sourceRoot": "", "sources": ["../../src/config/constants.ts"], "names": [], "mappings": ";;;AAAA,sCAAuD;AAG1C,QAAA,QAAQ,GAAG;IACtB,WAAW,EAAE,kCAAkC;IAC/C,WAAW,EAAE,kCAAkC;IAC/C,MAAM,EAAE,sBAAsB;CACtB,CAAC;AAGE,QAAA,gBAAgB,GAAqB;IAChD;QACE,IAAI,EAAE,SAAS;QACf,IAAI,EAAE,SAAS;QACf,OAAO,EAAE,aAAO,CAAC,WAAW;QAC5B,QAAQ,EAAE,gBAAgB;QAC1B,YAAY,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,qBAAqB,IAAI,GAAG,CAAC;KACjE;IACD;QACE,IAAI,EAAE,UAAU;QAChB,IAAI,EAAE,UAAU;QAChB,OAAO,EAAE,aAAO,CAAC,WAAW;QAC5B,QAAQ,EAAE,aAAa;QACvB,YAAY,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,sBAAsB,IAAI,GAAG,CAAC;KAClE;IACD;QACE,IAAI,EAAE,KAAK;QACX,IAAI,EAAE,KAAK;QACX,OAAO,EAAE,aAAO,CAAC,WAAW;QAC5B,QAAQ,EAAE,aAAa;QACvB,YAAY,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,iBAAiB,IAAI,GAAG,CAAC;KAC7D;IACD;QACE,IAAI,EAAE,WAAW;QACjB,IAAI,EAAE,WAAW;QACjB,OAAO,EAAE,aAAO,CAAC,WAAW;QAC5B,QAAQ,EAAE,kBAAkB;QAC5B,YAAY,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,uBAAuB,IAAI,GAAG,CAAC;KACnE;IACD;QACE,IAAI,EAAE,SAAS;QACf,IAAI,EAAE,SAAS;QACf,OAAO,EAAE,aAAO,CAAC,MAAM;QACvB,QAAQ,EAAE,UAAU;QACpB,YAAY,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,qBAAqB,IAAI,GAAG,CAAC;KACjE;CACF,CAAC;AAGW,QAAA,UAAU,GAAG;IACxB,KAAK,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,SAAS,IAAI,IAAI,CAAC;IAC9C,MAAM,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,IAAI,GAAG,CAAC;IAC/C,YAAY,EAAE,OAAO,CAAC,GAAG,CAAC,YAAY,KAAK,MAAM;CACzC,CAAC;AAGE,QAAA,qBAAqB,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,CAAC,CAAC;AAGxE,QAAA,SAAS,GAAG;IACvB,MAAM,EAAE,QAAQ;IAChB,UAAU,EAAE,YAAY;IACxB,gBAAgB,EAAE,kBAAkB;IACpC,mBAAmB,EAAE,qBAAqB;IAC1C,UAAU,EAAE,YAAY;IACxB,mBAAmB,EAAE,qBAAqB;CAClC,CAAC;AAGE,QAAA,YAAY,GAAG;IAC1B,WAAW,EAAE,CAAC;IACd,WAAW,EAAE,IAAI;IACjB,kBAAkB,EAAE,CAAC;CACb,CAAC;AAGE,QAAA,UAAU,GAAG;IACxB,KAAK,EAAE,OAAO;IACd,IAAI,EAAE,MAAM;IACZ,IAAI,EAAE,MAAM;IACZ,KAAK,EAAE,OAAO;CACN,CAAC"}