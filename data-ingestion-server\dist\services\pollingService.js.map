{"version": 3, "file": "pollingService.js", "sourceRoot": "", "sources": ["../../src/services/pollingService.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,gDAAkC;AAClC,6CAA0C;AAC1C,mDAAgD;AAChD,oDAAoE;AACpE,mDAAuD;AAEvD,4CAAyC;AAKzC,MAAa,cAAc;IAA3B;QACU,SAAI,GAAoC,IAAI,GAAG,EAAE,CAAC;QAClD,cAAS,GAAyB,IAAI,GAAG,EAAE,CAAC;IA+JtD,CAAC;IA1JC,KAAK;QACH,eAAM,CAAC,IAAI,CAAC,6BAA6B,CAAC,CAAC;QAE3C,4BAAgB,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;YAChC,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,IAAI,EAAE,MAAM,CAAC,YAAY,EAAE,MAAM,CAAC,OAAO,CAAC,CAAC;QACzE,CAAC,CAAC,CAAC;QAEH,eAAM,CAAC,IAAI,CAAC,WAAW,4BAAgB,CAAC,MAAM,eAAe,CAAC,CAAC;IACjE,CAAC;IAKD,IAAI;QACF,eAAM,CAAC,IAAI,CAAC,6BAA6B,CAAC,CAAC;QAE3C,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,GAAG,EAAE,QAAQ,EAAE,EAAE;YAClC,GAAG,CAAC,IAAI,EAAE,CAAC;YACX,eAAM,CAAC,IAAI,CAAC,2BAA2B,QAAQ,EAAE,CAAC,CAAC;QACrD,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;QAClB,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,CAAC;QACvB,eAAM,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC;IAC1C,CAAC;IAKO,eAAe,CAAC,YAAoB,EAAE,eAAuB,EAAE,OAAgB;QACrF,MAAM,cAAc,GAAG,KAAK,eAAe,UAAU,CAAC;QAEtD,MAAM,GAAG,GAAG,IAAI,CAAC,QAAQ,CAAC,cAAc,EAAE,KAAK,IAAI,EAAE;YACnD,MAAM,IAAI,CAAC,YAAY,CAAC,YAAY,EAAE,OAAO,CAAC,CAAC;QACjD,CAAC,EAAE;YACD,SAAS,EAAE,KAAK;SACjB,CAAC,CAAC;QAEH,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE,GAAG,CAAC,CAAC;QACjC,GAAG,CAAC,KAAK,EAAE,CAAC;QAEZ,eAAM,CAAC,IAAI,CAAC,2BAA2B,YAAY,WAAW,eAAe,WAAW,CAAC,CAAC;QAG1F,YAAY,CAAC,GAAG,EAAE;YAChB,IAAI,CAAC,YAAY,CAAC,YAAY,EAAE,OAAO,CAAC,CAAC;QAC3C,CAAC,CAAC,CAAC;IACL,CAAC;IAKO,KAAK,CAAC,YAAY,CAAC,YAAoB,EAAE,OAAgB;QAC/D,IAAI,CAAC;YACH,eAAM,CAAC,OAAO,CAAC,YAAY,EAAE,eAAe,CAAC,CAAC;YAG9C,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,qBAAqB,CAAC,YAAY,CAAC,CAAC;YAEjE,IAAI,CAAC,SAAS,EAAE,CAAC;gBACf,eAAM,CAAC,OAAO,CAAC,YAAY,EAAE,sCAAsC,CAAC,CAAC;gBACrE,OAAO;YACT,CAAC;YAED,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE,CAAC;gBAC9B,eAAM,CAAC,OAAO,CAAC,YAAY,EAAE,iCAAiC,EAAE,EAAE,IAAI,EAAE,OAAO,SAAS,EAAE,IAAI,EAAE,SAAS,EAAE,CAAC,CAAC;gBAC7G,OAAO;YACT,CAAC;YAED,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBAC3B,eAAM,CAAC,OAAO,CAAC,YAAY,EAAE,6BAA6B,CAAC,CAAC;gBAC5D,OAAO;YACT,CAAC;YAED,eAAM,CAAC,OAAO,CAAC,YAAY,EAAE,gBAAgB,EAAE,EAAE,KAAK,EAAE,SAAS,CAAC,MAAM,EAAE,CAAC,CAAC;YAG5E,MAAM,gBAAgB,GAAG,SAAS;iBAC/B,GAAG,CAAC,KAAK,CAAC,EAAE;gBACX,IAAI,CAAC;oBACH,OAAO,IAAA,2BAAc,EAAC,KAAK,EAAE,OAAO,CAAC,CAAC;gBACxC,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,eAAM,CAAC,KAAK,CAAC,gCAAgC,YAAY,GAAG,EAAE,KAAK,CAAC,CAAC;oBACrE,OAAO,IAAI,CAAC;gBACd,CAAC;YACH,CAAC,CAAC;iBACD,MAAM,CAAC,CAAC,KAAK,EAAsC,EAAE,CAAC,KAAK,KAAK,IAAI,CAAC;iBACrE,MAAM,CAAC,0BAAa,CAAC,CAAC;YAEzB,IAAI,gBAAgB,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBAClC,eAAM,CAAC,OAAO,CAAC,YAAY,EAAE,qCAAqC,CAAC,CAAC;gBACpE,OAAO;YACT,CAAC;YAED,eAAM,CAAC,OAAO,CAAC,YAAY,EAAE,mBAAmB,EAAE;gBAChD,KAAK,EAAE,gBAAgB,CAAC,MAAM;gBAC9B,QAAQ,EAAE,SAAS,CAAC,MAAM,GAAG,gBAAgB,CAAC,MAAM;aACrD,CAAC,CAAC;YAGH,MAAM,6BAAa,CAAC,oBAAoB,CAAC,gBAAgB,EAAE,YAAY,CAAC,CAAC;YAEzE,eAAM,CAAC,OAAO,CAAC,YAAY,EAAE,6BAA6B,CAAC,CAAC;QAE9D,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,sBAAsB,YAAY,GAAG,EAAE,KAAK,CAAC,CAAC;QAC7D,CAAC;IACH,CAAC;IAKO,KAAK,CAAC,qBAAqB,CAAC,YAAoB;QACtD,QAAQ,YAAY,EAAE,CAAC;YACrB,KAAK,SAAS;gBACZ,OAAO,uBAAU,CAAC,iBAAiB,EAAE,CAAC;YACxC,KAAK,UAAU;gBACb,OAAO,uBAAU,CAAC,mBAAmB,EAAE,CAAC;YAC1C,KAAK,KAAK;gBACR,OAAO,uBAAU,CAAC,cAAc,EAAE,CAAC;YACrC,KAAK,WAAW;gBACd,OAAO,uBAAU,CAAC,oBAAoB,EAAE,CAAC;YAC3C,KAAK,SAAS;gBACZ,OAAO,uBAAU,CAAC,kBAAkB,EAAE,CAAC;YACzC;gBACE,MAAM,IAAI,KAAK,CAAC,qBAAqB,YAAY,EAAE,CAAC,CAAC;QACzD,CAAC;IACH,CAAC;IAKD,SAAS;QACP,MAAM,MAAM,GAA4B,EAAE,CAAC;QAE3C,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,GAAG,EAAE,QAAQ,EAAE,EAAE;YAClC,MAAM,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,KAAK,CAAC;QAC3D,CAAC,CAAC,CAAC;QAEH,OAAO,MAAM,CAAC;IAChB,CAAC;IAKD,KAAK,CAAC,WAAW,CAAC,YAAoB;QACpC,MAAM,MAAM,GAAG,4BAAgB,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,YAAY,CAAC,CAAC;QACnE,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,MAAM,IAAI,KAAK,CAAC,qBAAqB,YAAY,EAAE,CAAC,CAAC;QACvD,CAAC;QAED,eAAM,CAAC,IAAI,CAAC,gCAAgC,YAAY,EAAE,CAAC,CAAC;QAC5D,MAAM,IAAI,CAAC,YAAY,CAAC,YAAY,EAAE,MAAM,CAAC,OAAO,CAAC,CAAC;IACxD,CAAC;CACF;AAjKD,wCAiKC;AAEY,QAAA,cAAc,GAAG,IAAI,cAAc,EAAE,CAAC"}