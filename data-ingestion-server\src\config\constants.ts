import { CategoryConfig, ApiType } from '../types/api';

// API Base URLs
export const API_URLS = {
  FRONTEND_V3: 'https://frontend-api-v3.pump.fun',
  ADVANCED_V2: 'https://advanced-api-v2.pump.fun',
  LEGACY: 'https://pump.fun/api'
} as const;

// Category configurations with their respective endpoints and polling intervals
export const CATEGORY_CONFIGS: CategoryConfig[] = [
  {
    slug: 'for-you',
    name: 'For You',
    apiType: ApiType.FRONTEND_V3,
    endpoint: '/coins/for-you',
    pollInterval: parseInt(process.env.POLL_INTERVAL_FOR_YOU || '5')
  },
  {
    slug: 'featured',
    name: 'Featured',
    apiType: ApiType.ADVANCED_V2,
    endpoint: '/coins/list',
    pollInterval: parseInt(process.env.POLL_INTERVAL_FEATURED || '5')
  },
  {
    slug: 'new',
    name: 'New',
    apiType: ApiType.ADVANCED_V2,
    endpoint: '/coins/list',
    pollInterval: parseInt(process.env.POLL_INTERVAL_NEW || '2')
  },
  {
    slug: 'graduated',
    name: 'Graduated',
    apiType: ApiType.ADVANCED_V2,
    endpoint: '/coins/graduated',
    pollInterval: parseInt(process.env.POLL_INTERVAL_GRADUATED || '5')
  },
  {
    slug: 'runners',
    name: 'Runners',
    apiType: ApiType.LEGACY,
    endpoint: '/runners',
    pollInterval: parseInt(process.env.POLL_INTERVAL_RUNNERS || '5')
  }
];

// API request parameters
export const API_PARAMS = {
  LIMIT: parseInt(process.env.API_LIMIT || '50'),
  OFFSET: parseInt(process.env.API_OFFSET || '0'),
  INCLUDE_NSFW: process.env.INCLUDE_NSFW === 'true'
} as const;

// Milestone multipliers for tracking token performance
export const MILESTONE_MULTIPLIERS = [2, 3, 5, 10, 20, 50, 100, 200, 300, 500, 1000];

// Database table names
export const DB_TABLES = {
  TOKENS: 'tokens',
  CATEGORIES: 'categories',
  TOKEN_CATEGORIES: 'token_categories',
  MARKET_DATA_HISTORY: 'market_data_history',
  MILESTONES: 'milestones',
  ACHIEVED_MILESTONES: 'achieved_milestones'
} as const;

// Retry configuration for API calls
export const RETRY_CONFIG = {
  MAX_RETRIES: 3,
  RETRY_DELAY: 1000, // milliseconds
  BACKOFF_MULTIPLIER: 2
} as const;

// Logging levels
export const LOG_LEVELS = {
  ERROR: 'error',
  WARN: 'warn',
  INFO: 'info',
  DEBUG: 'debug'
} as const;
