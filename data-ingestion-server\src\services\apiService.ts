import axios, { AxiosResponse } from 'axios';
import { API_URLS, API_PARAMS, RETRY_CONFIG } from '../config/constants';
import { ApiType, FrontendV3Token, AdvancedV2Token, LegacyToken } from '../types/api';
import { logger } from '../utils/logger';

/**
 * Service for making API requests to pump.fun endpoints
 */
export class ApiService {
  private useProxy: boolean;
  private proxyBaseUrl: string;

  constructor() {
    this.useProxy = process.env.USE_PROXY === 'true';
    this.proxyBaseUrl = process.env.PROXY_BASE_URL || 'http://localhost:3003/api/proxy';
  }

  /**
   * Make a request with retry logic
   */
  private async makeRequestWithRetry<T>(
    url: string,
    params: Record<string, any> = {},
    retryCount = 0
  ): Promise<T> {
    try {
      let requestUrl = url;
      let requestConfig: any = {
        timeout: 30000,
        headers: {
          'Accept': 'application/json',
          'Cache-Control': 'no-cache'
        }
      };

      // Use proxy if configured
      if (this.useProxy && !url.startsWith('http://localhost')) {
        const urlObj = new URL(url);
        const proxyUrl = new URL(this.proxyBaseUrl);
        
        proxyUrl.searchParams.set('url', urlObj.origin + urlObj.pathname);
        
        // Add original query parameters
        Object.entries(params).forEach(([key, value]) => {
          proxyUrl.searchParams.set(key, String(value));
        });
        
        requestUrl = proxyUrl.toString();
        requestConfig.params = {};
      } else {
        requestConfig.params = params;
      }

      logger.api('GET', requestUrl);
      logger.debug('Request config:', { url: requestUrl, params: requestConfig.params });

      const response: AxiosResponse<T> = await axios.get(requestUrl, requestConfig);

      // Handle different response formats
      let data = response.data;

      // Check if response is wrapped in a data.coins structure
      if (data && typeof data === 'object' && !Array.isArray(data) && (data as any).coins) {
        logger.debug('Unwrapping data.coins structure');
        data = (data as any).coins;
      }

      logger.api('GET', requestUrl, response.status, {
        dataLength: Array.isArray(data) ? data.length : 'N/A',
        dataType: typeof data,
        isWrapped: response.data !== data,
        sampleData: Array.isArray(data) && data.length > 0 ?
          { firstItem: Object.keys(data[0] || {}) } : 'N/A'
      });

      return data;
    } catch (error: any) {
      const errorMessage = error.response?.data?.message || error.message || 'Unknown error';
      const statusCode = error.response?.status;
      
      logger.error(`API request failed (attempt ${retryCount + 1}/${RETRY_CONFIG.MAX_RETRIES + 1}):`, {
        url,
        status: statusCode,
        error: errorMessage
      });

      // Retry logic
      if (retryCount < RETRY_CONFIG.MAX_RETRIES) {
        const delay = RETRY_CONFIG.RETRY_DELAY * Math.pow(RETRY_CONFIG.BACKOFF_MULTIPLIER, retryCount);
        logger.info(`Retrying in ${delay}ms...`);
        
        await new Promise(resolve => setTimeout(resolve, delay));
        return this.makeRequestWithRetry<T>(url, params, retryCount + 1);
      }

      throw new Error(`API request failed after ${RETRY_CONFIG.MAX_RETRIES + 1} attempts: ${errorMessage}`);
    }
  }

  /**
   * Fetch For You recommendations
   */
  async fetchForYouTokens(): Promise<FrontendV3Token[]> {
    const url = `${API_URLS.FRONTEND_V3}/coins/for-you`;
    const params = {
      offset: API_PARAMS.OFFSET,
      limit: API_PARAMS.LIMIT,
      includeNsfw: API_PARAMS.INCLUDE_NSFW
    };

    return this.makeRequestWithRetry<FrontendV3Token[]>(url, params);
  }

  /**
   * Fetch Featured tokens
   */
  async fetchFeaturedTokens(): Promise<AdvancedV2Token[]> {
    const url = `${API_URLS.ADVANCED_V2}/coins/list`;
    const params = {
      sortBy: 'featured',
      limit: API_PARAMS.LIMIT,
      offset: API_PARAMS.OFFSET
    };

    return this.makeRequestWithRetry<AdvancedV2Token[]>(url, params);
  }

  /**
   * Fetch New tokens
   */
  async fetchNewTokens(): Promise<AdvancedV2Token[]> {
    const url = `${API_URLS.ADVANCED_V2}/coins/list`;
    const params = {
      sortBy: 'new',
      limit: API_PARAMS.LIMIT,
      offset: API_PARAMS.OFFSET
    };

    return this.makeRequestWithRetry<AdvancedV2Token[]>(url, params);
  }

  /**
   * Fetch Graduated tokens
   */
  async fetchGraduatedTokens(): Promise<AdvancedV2Token[]> {
    const url = `${API_URLS.ADVANCED_V2}/coins/graduated`;
    const params = {
      limit: API_PARAMS.LIMIT,
      offset: API_PARAMS.OFFSET
    };

    return this.makeRequestWithRetry<AdvancedV2Token[]>(url, params);
  }

  /**
   * Fetch Runners tokens
   */
  async fetchRunnersTokens(): Promise<LegacyToken[]> {
    const url = `${API_URLS.LEGACY}/runners`;
    const params = {
      limit: API_PARAMS.LIMIT,
      offset: API_PARAMS.OFFSET
    };

    return this.makeRequestWithRetry<LegacyToken[]>(url, params);
  }
}

export const apiService = new ApiService();
