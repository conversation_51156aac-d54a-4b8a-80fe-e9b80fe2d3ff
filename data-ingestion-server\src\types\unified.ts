/**
 * Unified data model for pump.fun tokens
 * This represents the normalized format that all API responses are converted to
 */
export interface UnifiedToken {
  // --- Core Identifiers (Common to all) ---
  mint: string;
  name: string;
  symbol: string;
  creator: string;

  // --- Core Metadata (Common to all) ---
  description: string;
  curatedDescription?: string;
  imageUrl?: string;
  videoUrl?: string;
  bannerUrl?: string;
  websiteUrl?: string;
  twitterUrl?: string;
  telegramUrl?: string;
  metadataUrl?: string;
  dexscreenerUrl?: string;

  // --- Market & Financial Data ---
  marketCapUsd?: number;
  marketCapSol?: number;
  allTimeHighMarketCapSol?: number;
  volume24h?: number;
  totalSupply?: number;
  currentMarketPrice?: number;
  fdv?: number;
  priceUsd?: number;
  priceNative?: number;
  priceChangeM5?: number;
  priceChangeH1?: number;
  priceChangeH6?: number;
  priceChangeH24?: number;

  // --- Transaction & Holder Analytics ---
  numHolders?: number;
  topHoldersPercentage?: number;
  devHoldingsPercentage?: number;
  buyTransactions?: number;
  sellTransactions?: number;
  totalTransactions?: number;
  holders?: TokenHolder[];

  // --- Sniper Analytics ---
  sniperCount?: number;
  sniperOwnedPercentage?: number;

  // --- Pump.fun Lifecycle Status ---
  creationTimestamp?: number;
  graduationTimestamp?: number;
  kingOfTheHillTimestamp?: number;
  bondingCurveProgress?: number;
  isComplete?: boolean;
  isLive?: boolean;

  // --- Pump.fun Specific IDs & Data ---
  bondingCurveAddress?: string;
  associatedBondingCurve?: string;
  raydiumPoolAddress?: string;
  virtualSolReserves?: number;
  virtualTokenReserves?: number;

  // --- Social & Status Flags ---
  replyCount?: number;
  lastReplyTimestamp?: number;
  hasSocial?: boolean;
  isNsfw?: boolean;
  isBanned?: boolean;
  twitterReuseCount?: number;
}

export interface TokenHolder {
  holderId: string;
  totalTokenAmountHeld: number;
  ownedPercentage: number;
  isSniper: boolean;
}

export interface DatabaseToken {
  // Core identifiers
  mint: string;
  name: string;
  symbol: string;
  creator: string;

  // Metadata
  description?: string;
  curated_description?: string;
  image_url?: string;
  video_url?: string;
  banner_url?: string;
  website_url?: string;
  twitter_url?: string;
  telegram_url?: string;
  metadata_url?: string;
  dexscreener_url?: string;

  // Market data
  initial_market_cap_usd: number;
  current_market_cap_usd?: number;
  fdv?: number;
  price_usd?: number;
  price_native?: number;
  price_change_m5?: number;
  price_change_h1?: number;
  price_change_h6?: number;
  price_change_h24?: number;

  // Pump.fun specific
  bonding_curve_address?: string;
  associated_bonding_curve?: string;
  raydium_pool_address?: string;
  graduation_timestamp?: string;

  // Flags
  is_nsfw?: boolean;
  is_banned?: boolean;

  // Timestamps
  created_at?: string;
  updated_at?: string;
}

export interface Category {
  id: number;
  name: string;
  slug: string;
}

export interface TokenCategory {
  token_mint: string;
  category_id: number;
  created_at?: string;
}

export interface MarketDataHistory {
  timestamp: string;
  token_mint: string;
  market_cap_usd?: number;
  market_cap_sol?: number;
  volume_24h?: number;
  num_holders?: number;
  // DexScreener transaction data
  txns_m5_buys?: number;
  txns_m5_sells?: number;
  txns_h1_buys?: number;
  txns_h1_sells?: number;
  txns_h6_buys?: number;
  txns_h6_sells?: number;
  txns_h24_buys?: number;
  txns_h24_sells?: number;
}

export interface Milestone {
  id: number;
  multiplier: number;
  name: string;
}

export interface AchievedMilestone {
  token_mint: string;
  milestone_id: number;
  achieved_at_timestamp: string;
  market_cap_at_milestone: number;
}
