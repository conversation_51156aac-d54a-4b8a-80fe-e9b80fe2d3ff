export interface DexScreenerTokenData {
    chainId: string;
    dexId: string;
    url: string;
    pairAddress: string;
    baseToken: {
        address: string;
        name: string;
        symbol: string;
    };
    priceNative: string;
    priceUsd: string;
    txns: {
        m5: {
            buys: number;
            sells: number;
        };
        h1: {
            buys: number;
            sells: number;
        };
        h6: {
            buys: number;
            sells: number;
        };
        h24: {
            buys: number;
            sells: number;
        };
    };
    volume: {
        h24: number;
    };
    priceChange: {
        m5: number;
        h1: number;
        h6: number;
        h24: number;
    };
    fdv: number;
    marketCap: number;
    pairCreatedAt: number;
}
export interface EnrichmentData {
    dexscreenerUrl: string;
    fdv: number;
    priceNative: number;
    priceUsd: number;
    priceChangeM5: number;
    priceChangeH1: number;
    priceChangeH6: number;
    priceChangeH24: number;
    graduationTimestamp: number;
    txnsM5Buys: number;
    txnsM5Sells: number;
    txnsH1Buys: number;
    txnsH1Sells: number;
    txnsH6Buys: number;
    txnsH6Sells: number;
    txnsH24Buys: number;
    txnsH24Sells: number;
}
export declare class EnrichmentService {
    private readonly baseUrl;
    enrichToken(mint: string): Promise<EnrichmentData | null>;
    enrichTokens(mints: string[]): Promise<Map<string, EnrichmentData>>;
    private makeRequestWithRetry;
}
export declare const enrichmentService: EnrichmentService;
//# sourceMappingURL=enrichmentService.d.ts.map