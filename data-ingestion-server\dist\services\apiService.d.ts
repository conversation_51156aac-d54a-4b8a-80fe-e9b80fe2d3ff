import { FrontendV3Token, AdvancedV2Token, LegacyToken } from '../types/api';
export declare class ApiService {
    private useProxy;
    private proxyBaseUrl;
    constructor();
    private makeRequestWithRetry;
    fetchForYouTokens(): Promise<FrontendV3Token[]>;
    fetchFeaturedTokens(): Promise<AdvancedV2Token[]>;
    fetchNewTokens(): Promise<AdvancedV2Token[]>;
    fetchGraduatedTokens(): Promise<AdvancedV2Token[]>;
    fetchRunnersTokens(): Promise<LegacyToken[]>;
}
export declare const apiService: ApiService;
//# sourceMappingURL=apiService.d.ts.map