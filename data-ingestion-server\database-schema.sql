-- ================================================================= 
--  Pump.fun Data Ingestion Server Database Schema
--  Complete schema for Supabase PostgreSQL database
-- ================================================================= 

-- ================================================================= 
--  Part 1: Core Tables for Tokens and Categories 
-- ================================================================= 

-- Create the main 'tokens' table to store core, relatively static information. 
-- The mint address is the natural primary key. 
CREATE TABLE public.tokens ( 
    -- Core Identifiers 
    mint TEXT PRIMARY KEY, 
    name TEXT, 
    symbol TEXT, 
    creator TEXT, 
 
    -- Core Metadata 
    description TEXT, 
    curated_description TEXT, 
    image_url TEXT, 
    video_url TEXT, 
    banner_url TEXT, 
    website_url TEXT, 
    twitter_url TEXT, 
    telegram_url TEXT, 
    metadata_url TEXT, 
     
    -- Initial & Current State (for quick access) 
    initial_market_cap_usd NUMERIC NOT NULL, 
    current_market_cap_usd NUMERIC, 
     
    -- Pump.fun Specific IDs 
    bonding_curve_address TEXT, 
    associated_bonding_curve TEXT, 
    raydium_pool_address TEXT, 
     
    -- Timestamps 
    created_at TIMESTAMPTZ DEFAULT now() NOT NULL, 
    updated_at TIMESTAMPTZ DEFAULT now() NOT NULL, 
 
    -- Other flags/static data from your schema 
    is_nsfw BOOLEAN DEFAULT false, 
    is_banned BOOLEAN DEFAULT false 
); 
 
COMMENT ON COLUMN public.tokens.initial_market_cap_usd IS 'The market cap recorded when the token was first added. Used for milestone calculations.'; 
COMMENT ON COLUMN public.tokens.current_market_cap_usd IS 'The most recently recorded market cap for quick lookups.'; 

-- Create a lookup table for the categories. This makes the system extensible. 
CREATE TABLE public.categories ( 
    id SERIAL PRIMARY KEY, 
    name TEXT NOT NULL UNIQUE, 
    slug TEXT NOT NULL UNIQUE -- A URL-friendly version of the name, e.g., 'for-you' 
); 
 
COMMENT ON TABLE public.categories IS 'A lookup table for the different token categories.'; 

-- Create the join table to link tokens and categories (many-to-many). 
-- This table will be updated frequently as tokens move between categories. 
CREATE TABLE public.token_categories ( 
    token_mint TEXT NOT NULL REFERENCES public.tokens(mint) ON DELETE CASCADE, 
    category_id INT NOT NULL REFERENCES public.categories(id) ON DELETE CASCADE, 
    created_at TIMESTAMPTZ DEFAULT now() NOT NULL, 
    PRIMARY KEY (token_mint, category_id) -- Ensures a token can't be in the same category twice. 
); 
 
COMMENT ON TABLE public.token_categories IS 'Links tokens to their current categories. This is a many-to-many relationship.'; 

-- ================================================================= 
--  Part 2: Tables for High-Frequency and Historical Data 
-- ================================================================= 

-- For high-frequency market data, a separate time-series table is crucial. 
-- IMPORTANT: For best performance in Supabase, enable the TimescaleDB extension on your database 
-- and run the line: SELECT create_hypertable('market_data_history', 'timestamp'); 
CREATE TABLE public.market_data_history ( 
    "timestamp" TIMESTAMPTZ NOT NULL, 
    token_mint TEXT NOT NULL REFERENCES public.tokens(mint) ON DELETE CASCADE, 
    market_cap_usd NUMERIC, 
    market_cap_sol NUMERIC, 
    volume_24h NUMERIC, 
    num_holders INT, 
    -- Add any other fields you want to track historically 
    PRIMARY KEY (token_mint, "timestamp") 
); 
 
COMMENT ON TABLE public.market_data_history IS 'Stores historical time-series data for each token to track its performance over time.'; 

-- Create a lookup table for the milestone multipliers. 
CREATE TABLE public.milestones ( 
    id SERIAL PRIMARY KEY, 
    multiplier INT NOT NULL UNIQUE, 
    name TEXT NOT NULL UNIQUE -- e.g., '2x', '10x', '100x' 
); 
 
COMMENT ON TABLE public.milestones IS 'A lookup table for market cap increase milestones.'; 

-- Create a table to track when a token achieves a milestone. 
CREATE TABLE public.achieved_milestones ( 
    token_mint TEXT NOT NULL REFERENCES public.tokens(mint) ON DELETE CASCADE, 
    milestone_id INT NOT NULL REFERENCES public.milestones(id) ON DELETE CASCADE, 
    achieved_at_timestamp TIMESTAMPTZ NOT NULL, 
    market_cap_at_milestone NUMERIC NOT NULL, 
    PRIMARY KEY (token_mint, milestone_id) -- A token can only achieve each milestone once. 
); 
 
COMMENT ON TABLE public.achieved_milestones IS 'Tracks when a token achieves a specific market cap milestone.'; 

-- ================================================================= 
--  Part 3: Initial Data Population for Lookup Tables 
-- ================================================================= 

-- Populate the categories table with your initial set. 
INSERT INTO public.categories (name, slug) 
VALUES 
    ('New', 'new'), 
    ('Runners', 'runners'), 
    ('For You', 'for-you'), 
    ('Featured', 'featured'),
    ('Graduated', 'graduated'); 

-- Populate the milestones table. 
INSERT INTO public.milestones (multiplier, name) 
VALUES 
    (2, '2x'), 
    (3, '3x'), 
    (5, '5x'), 
    (10, '10x'), 
    (20, '20x'), 
    (50, '50x'), 
    (100, '100x'), 
    (200, '200x'), 
    (300, '300x'), 
    (500, '500x'), 
    (1000, '1000x'); 

-- ================================================================= 
--  Part 4: Indexes and Triggers for Performance 
-- ================================================================= 

-- Create indexes on foreign keys for faster joins. 
CREATE INDEX ON public.token_categories (token_mint); 
CREATE INDEX ON public.token_categories (category_id); 
CREATE INDEX ON public.market_data_history (token_mint, "timestamp" DESC); 
CREATE INDEX ON public.achieved_milestones (token_mint); 

-- Create a function to automatically update the `updated_at` timestamp. 
CREATE OR REPLACE FUNCTION public.handle_updated_at() 
RETURNS TRIGGER AS $$ 
BEGIN 
    NEW.updated_at = now(); 
    RETURN NEW; 
END; 
$$ LANGUAGE plpgsql; 

-- Apply the trigger to the `tokens` table. 
CREATE TRIGGER on_tokens_update 
BEFORE UPDATE ON public.tokens 
FOR EACH ROW 
EXECUTE PROCEDURE public.handle_updated_at(); 

-- ================================================================= 
--  Part 5: Row Level Security (RLS) - A Supabase Best Practice 
-- ================================================================= 

-- Enable RLS on all tables. 
ALTER TABLE public.tokens ENABLE ROW LEVEL SECURITY; 
ALTER TABLE public.categories ENABLE ROW LEVEL SECURITY; 
ALTER TABLE public.token_categories ENABLE ROW LEVEL SECURITY; 
ALTER TABLE public.market_data_history ENABLE ROW LEVEL SECURITY; 
ALTER TABLE public.milestones ENABLE ROW LEVEL SECURITY; 
ALTER TABLE public.achieved_milestones ENABLE ROW LEVEL SECURITY; 

-- Create basic policies to allow public read-only access. 
-- You can create more specific policies for authenticated users to write data. 
CREATE POLICY "Allow public read access" ON public.tokens FOR SELECT USING (true); 
CREATE POLICY "Allow public read access" ON public.categories FOR SELECT USING (true); 
CREATE POLICY "Allow public read access" ON public.token_categories FOR SELECT USING (true); 
CREATE POLICY "Allow public read access" ON public.market_data_history FOR SELECT USING (true); 
CREATE POLICY "Allow public read access" ON public.milestones FOR SELECT USING (true); 
CREATE POLICY "Allow public read access" ON public.achieved_milestones FOR SELECT USING (true);

-- Create policies to allow anon key to write data (for server operations)
CREATE POLICY "Allow anon write access" ON public.tokens FOR ALL USING (true);
CREATE POLICY "Allow anon write access" ON public.categories FOR ALL USING (true);
CREATE POLICY "Allow anon write access" ON public.token_categories FOR ALL USING (true);
CREATE POLICY "Allow anon write access" ON public.market_data_history FOR ALL USING (true);
CREATE POLICY "Allow anon write access" ON public.milestones FOR ALL USING (true);
CREATE POLICY "Allow anon write access" ON public.achieved_milestones FOR ALL USING (true);

-- Create policies to allow service role full access
CREATE POLICY "Allow service role full access" ON public.tokens FOR ALL USING (auth.role() = 'service_role');
CREATE POLICY "Allow service role full access" ON public.categories FOR ALL USING (auth.role() = 'service_role');
CREATE POLICY "Allow service role full access" ON public.token_categories FOR ALL USING (auth.role() = 'service_role');
CREATE POLICY "Allow service role full access" ON public.market_data_history FOR ALL USING (auth.role() = 'service_role');
CREATE POLICY "Allow service role full access" ON public.milestones FOR ALL USING (auth.role() = 'service_role');
CREATE POLICY "Allow service role full access" ON public.achieved_milestones FOR ALL USING (auth.role() = 'service_role');
