import { UnifiedToken } from '../types/unified';
import { ApiType } from '../types/api';
export declare class TokenNormalizer {
    static normalizeToken(rawToken: any, apiType: ApiType): UnifiedToken;
    private static normalizeFrontendV3Token;
    private static normalizeAdvancedV2Token;
    private static normalizeLegacyToken;
    static validateToken(token: UnifiedToken): boolean;
}
export declare const normalizeToken: typeof TokenNormalizer.normalizeToken;
export declare const validateToken: typeof TokenNormalizer.validateToken;
//# sourceMappingURL=normalizer.d.ts.map