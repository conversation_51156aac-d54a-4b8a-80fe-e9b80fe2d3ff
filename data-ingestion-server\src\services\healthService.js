"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === "function" ? Iterator : Object).prototype);
    return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2), typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.healthService = exports.HealthService = void 0;
var express_1 = require("express");
var supabaseClient_1 = require("./supabaseClient");
var pollingService_1 = require("./pollingService");
var logger_1 = require("../utils/logger");
/**
 * Simple health check service for monitoring
 */
var HealthService = /** @class */ (function () {
    function HealthService() {
        this.app = (0, express_1.default)();
        this.setupRoutes();
    }
    /**
     * Setup health check routes
     */
    HealthService.prototype.setupRoutes = function () {
        var _this = this;
        this.app.get('/health', function (req, res) { return __awaiter(_this, void 0, void 0, function () {
            var health, statusCode, error_1;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        _a.trys.push([0, 2, , 3]);
                        return [4 /*yield*/, this.getHealthStatus()];
                    case 1:
                        health = _a.sent();
                        statusCode = health.status === 'healthy' ? 200 : 503;
                        res.status(statusCode).json(health);
                        return [3 /*break*/, 3];
                    case 2:
                        error_1 = _a.sent();
                        logger_1.logger.error('Health check failed:', error_1);
                        res.status(503).json({
                            status: 'unhealthy',
                            error: 'Health check failed'
                        });
                        return [3 /*break*/, 3];
                    case 3: return [2 /*return*/];
                }
            });
        }); });
        this.app.get('/status', function (req, res) {
            var pollingStatus = pollingService_1.pollingService.getStatus();
            res.json({
                status: 'running',
                polling: pollingStatus,
                uptime: process.uptime(),
                memory: process.memoryUsage(),
                timestamp: new Date().toISOString()
            });
        });
    };
    /**
     * Get comprehensive health status
     */
    HealthService.prototype.getHealthStatus = function () {
        return __awaiter(this, void 0, void 0, function () {
            var dbHealthy, pollingStatus, pollingHealthy, status;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, supabaseClient_1.supabaseService.testConnection()];
                    case 1:
                        dbHealthy = _a.sent();
                        pollingStatus = pollingService_1.pollingService.getStatus();
                        pollingHealthy = Object.values(pollingStatus).some(function (running) { return running; });
                        status = dbHealthy && pollingHealthy ? 'healthy' : 'unhealthy';
                        return [2 /*return*/, {
                                status: status,
                                timestamp: new Date().toISOString(),
                                uptime: process.uptime(),
                                database: dbHealthy ? 'connected' : 'disconnected',
                                polling: {
                                    status: pollingHealthy ? 'running' : 'stopped',
                                    jobs: pollingStatus
                                },
                                memory: process.memoryUsage()
                            }];
                }
            });
        });
    };
    /**
     * Start the health service
     */
    HealthService.prototype.start = function (port) {
        if (port === void 0) { port = 3005; }
        this.server = this.app.listen(port, function () {
            logger_1.logger.info("Health service running on port ".concat(port));
            logger_1.logger.info("Health check: http://localhost:".concat(port, "/health"));
            logger_1.logger.info("Status endpoint: http://localhost:".concat(port, "/status"));
        });
    };
    /**
     * Stop the health service
     */
    HealthService.prototype.stop = function () {
        if (this.server) {
            this.server.close();
            logger_1.logger.info('Health service stopped');
        }
    };
    return HealthService;
}());
exports.HealthService = HealthService;
exports.healthService = new HealthService();
