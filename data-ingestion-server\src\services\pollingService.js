"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === "function" ? Iterator : Object).prototype);
    return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2), typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.pollingService = exports.PollingService = void 0;
var cron = require("node-cron");
var apiService_1 = require("./apiService");
var dataProcessor_1 = require("./dataProcessor");
var normalizer_1 = require("../utils/normalizer");
var constants_1 = require("../config/constants");
var logger_1 = require("../utils/logger");
/**
 * Service that manages all polling jobs for different token categories
 */
var PollingService = /** @class */ (function () {
    function PollingService() {
        this.jobs = new Map();
    }
    /**
     * Start all polling jobs
     */
    PollingService.prototype.start = function () {
        var _this = this;
        logger_1.logger.info('Starting polling service...');
        constants_1.CATEGORY_CONFIGS.forEach(function (config) {
            _this.startPollingJob(config.slug, config.pollInterval, config.apiType);
        });
        logger_1.logger.info("Started ".concat(constants_1.CATEGORY_CONFIGS.length, " polling jobs"));
    };
    /**
     * Stop all polling jobs
     */
    PollingService.prototype.stop = function () {
        logger_1.logger.info('Stopping polling service...');
        this.jobs.forEach(function (job, category) {
            job.stop();
            logger_1.logger.info("Stopped polling job for ".concat(category));
        });
        this.jobs.clear();
        logger_1.logger.info('All polling jobs stopped');
    };
    /**
     * Start a polling job for a specific category
     */
    PollingService.prototype.startPollingJob = function (categorySlug, intervalMinutes, apiType) {
        var _this = this;
        var cronExpression = "*/".concat(intervalMinutes, " * * * *"); // Every N minutes
        var job = cron.schedule(cronExpression, function () { return __awaiter(_this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, this.pollCategory(categorySlug, apiType)];
                    case 1:
                        _a.sent();
                        return [2 /*return*/];
                }
            });
        }); }, {
            scheduled: false // Don't start immediately
        });
        this.jobs.set(categorySlug, job);
        job.start();
        logger_1.logger.info("Started polling job for ".concat(categorySlug, " (every ").concat(intervalMinutes, " minutes)"));
        // Run immediately on startup
        setImmediate(function () {
            _this.pollCategory(categorySlug, apiType);
        });
    };
    /**
     * Poll a specific category and process the data
     */
    PollingService.prototype.pollCategory = function (categorySlug, apiType) {
        return __awaiter(this, void 0, void 0, function () {
            var rawTokens, normalizedTokens, error_1;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        _a.trys.push([0, 3, , 4]);
                        logger_1.logger.polling(categorySlug, 'Starting poll');
                        return [4 /*yield*/, this.fetchTokensByCategory(categorySlug)];
                    case 1:
                        rawTokens = _a.sent();
                        if (!rawTokens || rawTokens.length === 0) {
                            logger_1.logger.polling(categorySlug, 'No tokens returned from API');
                            return [2 /*return*/];
                        }
                        logger_1.logger.polling(categorySlug, 'Fetched tokens', { count: rawTokens.length });
                        normalizedTokens = rawTokens
                            .map(function (token) {
                            try {
                                return (0, normalizer_1.normalizeToken)(token, apiType);
                            }
                            catch (error) {
                                logger_1.logger.error("Failed to normalize token in ".concat(categorySlug, ":"), error);
                                return null;
                            }
                        })
                            .filter(function (token) { return token !== null; })
                            .filter(normalizer_1.validateToken);
                        if (normalizedTokens.length === 0) {
                            logger_1.logger.polling(categorySlug, 'No valid tokens after normalization');
                            return [2 /*return*/];
                        }
                        logger_1.logger.polling(categorySlug, 'Normalized tokens', {
                            count: normalizedTokens.length,
                            filtered: rawTokens.length - normalizedTokens.length
                        });
                        // Process and upsert data
                        return [4 /*yield*/, dataProcessor_1.dataProcessor.processAndUpsertData(normalizedTokens, categorySlug)];
                    case 2:
                        // Process and upsert data
                        _a.sent();
                        logger_1.logger.polling(categorySlug, 'Poll completed successfully');
                        return [3 /*break*/, 4];
                    case 3:
                        error_1 = _a.sent();
                        logger_1.logger.error("Polling failed for ".concat(categorySlug, ":"), error_1);
                        return [3 /*break*/, 4];
                    case 4: return [2 /*return*/];
                }
            });
        });
    };
    /**
     * Fetch tokens based on category slug
     */
    PollingService.prototype.fetchTokensByCategory = function (categorySlug) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                switch (categorySlug) {
                    case 'for-you':
                        return [2 /*return*/, apiService_1.apiService.fetchForYouTokens()];
                    case 'featured':
                        return [2 /*return*/, apiService_1.apiService.fetchFeaturedTokens()];
                    case 'new':
                        return [2 /*return*/, apiService_1.apiService.fetchNewTokens()];
                    case 'graduated':
                        return [2 /*return*/, apiService_1.apiService.fetchGraduatedTokens()];
                    case 'runners':
                        return [2 /*return*/, apiService_1.apiService.fetchRunnersTokens()];
                    default:
                        throw new Error("Unknown category: ".concat(categorySlug));
                }
                return [2 /*return*/];
            });
        });
    };
    /**
     * Get status of all polling jobs
     */
    PollingService.prototype.getStatus = function () {
        var status = {};
        this.jobs.forEach(function (job, category) {
            status[category] = job.running;
        });
        return status;
    };
    /**
     * Manually trigger a poll for a specific category
     */
    PollingService.prototype.triggerPoll = function (categorySlug) {
        return __awaiter(this, void 0, void 0, function () {
            var config;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        config = constants_1.CATEGORY_CONFIGS.find(function (c) { return c.slug === categorySlug; });
                        if (!config) {
                            throw new Error("Unknown category: ".concat(categorySlug));
                        }
                        logger_1.logger.info("Manually triggering poll for ".concat(categorySlug));
                        return [4 /*yield*/, this.pollCategory(categorySlug, config.apiType)];
                    case 1:
                        _a.sent();
                        return [2 /*return*/];
                }
            });
        });
    };
    return PollingService;
}());
exports.PollingService = PollingService;
exports.pollingService = new PollingService();
