export interface FrontendV3Token {
    mint: string;
    name: string;
    symbol: string;
    description: string;
    image_uri?: string;
    metadata_uri?: string;
    creator: string;
    usd_market_cap?: number;
    market_cap?: number;
    volume_24h?: number;
    created_timestamp?: number;
    nsfw?: boolean;
    complete?: boolean;
    is_currently_live?: boolean;
    bonding_curve?: string;
    associated_bonding_curve?: string;
    raydium_pool?: string;
    website?: string;
    twitter?: string;
    telegram?: string;
}
export interface AdvancedV2Token {
    mint: string;
    name: string;
    symbol: string;
    description: string;
    image_uri?: string;
    metadata_uri?: string;
    creator: string;
    usd_market_cap?: number;
    market_cap?: number;
    volume_24h?: number;
    created_timestamp?: number;
    graduation_timestamp?: number;
    nsfw?: boolean;
    complete?: boolean;
    is_currently_live?: boolean;
    bonding_curve?: string;
    associated_bonding_curve?: string;
    raydium_pool?: string;
    website?: string;
    twitter?: string;
    telegram?: string;
    virtual_sol_reserves?: number;
    virtual_token_reserves?: number;
    total_supply?: number;
    reply_count?: number;
    last_reply?: number;
}
export interface LegacyToken {
    coinMint: string;
    name: string;
    ticker: string;
    description: string;
    curatedDescription?: string;
    imageUrl?: string;
    videoUrl?: string;
    bannerUrl?: string;
    metadataUrl?: string;
    dev: string;
    marketCap?: number;
    volume?: number;
    creationTime?: number;
    graduationDate?: number;
    nsfw?: boolean;
    complete?: boolean;
    poolAddress?: string;
    website?: string;
    twitter?: string;
    telegram?: string;
    transactions?: number;
    holders?: LegacyHolder[];
    sniperCount?: number;
    sniperOwnedPercentage?: number;
    topHoldersPercentage?: number;
    devHoldingsPercentage?: number;
    buyTransactions?: number;
    sellTransactions?: number;
    allTimeHighMarketCap?: number;
    bondingCurveProgress?: number;
    virtualSolReserves?: number;
    virtualTokenReserves?: number;
    totalSupply?: number;
    currentMarketPrice?: number;
    replyCount?: number;
    lastReplyTimestamp?: number;
    twitterReuseCount?: number;
}
export interface LegacyHolder {
    holderId: string;
    totalTokenAmountHeld: number;
    ownedPercentage: number;
    isSniper: boolean;
}
export interface ApiResponse<T> {
    data: T[];
    success: boolean;
    error?: string;
}
export declare enum ApiType {
    FRONTEND_V3 = "frontend_v3",
    ADVANCED_V2 = "advanced_v2",
    LEGACY = "legacy"
}
export interface CategoryConfig {
    slug: string;
    name: string;
    apiType: ApiType;
    endpoint: string;
    pollInterval: number;
}
//# sourceMappingURL=api.d.ts.map