"use strict";
/**
 * Type definitions for API responses from different pump.fun endpoints
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.ApiType = void 0;
// Enum for API types to help with normalization
var ApiType;
(function (ApiType) {
    ApiType["FRONTEND_V3"] = "frontend_v3";
    ApiType["ADVANCED_V2"] = "advanced_v2";
    ApiType["LEGACY"] = "legacy";
})(ApiType || (exports.ApiType = ApiType = {}));
