"use strict";
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === "function" ? Iterator : Object).prototype);
    return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2), typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.dataProcessor = exports.DataProcessor = void 0;
var supabaseClient_1 = require("./supabaseClient");
var constants_1 = require("../config/constants");
var logger_1 = require("../utils/logger");
/**
 * Handles all database operations for token data processing
 */
var DataProcessor = /** @class */ (function () {
    function DataProcessor() {
    }
    /**
     * Main function to process and upsert a batch of tokens for a specific category
     */
    DataProcessor.prototype.processAndUpsertData = function (tokens, categorySlug) {
        return __awaiter(this, void 0, void 0, function () {
            var category, _i, tokens_1, token, error_1;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        if (tokens.length === 0) {
                            logger_1.logger.warn("No tokens to process for category: ".concat(categorySlug));
                            return [2 /*return*/];
                        }
                        logger_1.logger.polling(categorySlug, 'Processing tokens', { count: tokens.length });
                        _a.label = 1;
                    case 1:
                        _a.trys.push([1, 8, , 9]);
                        return [4 /*yield*/, this.getCategoryBySlug(categorySlug)];
                    case 2:
                        category = _a.sent();
                        if (!category) {
                            throw new Error("Category not found: ".concat(categorySlug));
                        }
                        _i = 0, tokens_1 = tokens;
                        _a.label = 3;
                    case 3:
                        if (!(_i < tokens_1.length)) return [3 /*break*/, 6];
                        token = tokens_1[_i];
                        return [4 /*yield*/, this.processToken(token, category.id)];
                    case 4:
                        _a.sent();
                        _a.label = 5;
                    case 5:
                        _i++;
                        return [3 /*break*/, 3];
                    case 6: 
                    // Update category associations
                    return [4 /*yield*/, this.updateCategoryAssociations(tokens.map(function (t) { return t.mint; }), category.id)];
                    case 7:
                        // Update category associations
                        _a.sent();
                        logger_1.logger.polling(categorySlug, 'Successfully processed tokens', { count: tokens.length });
                        return [3 /*break*/, 9];
                    case 8:
                        error_1 = _a.sent();
                        logger_1.logger.error("Failed to process tokens for category ".concat(categorySlug, ":"), error_1);
                        throw error_1;
                    case 9: return [2 /*return*/];
                }
            });
        });
    };
    /**
     * Process a single token: upsert, add market history, check milestones
     */
    DataProcessor.prototype.processToken = function (token, categoryId) {
        return __awaiter(this, void 0, void 0, function () {
            var dbToken, error_2;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        _a.trys.push([0, 6, , 7]);
                        dbToken = this.convertToDbFormat(token);
                        // Upsert token
                        return [4 /*yield*/, this.upsertToken(dbToken)];
                    case 1:
                        // Upsert token
                        _a.sent();
                        if (!(token.marketCapUsd || token.marketCapSol || token.volume24h)) return [3 /*break*/, 3];
                        return [4 /*yield*/, this.addMarketDataHistory(token)];
                    case 2:
                        _a.sent();
                        _a.label = 3;
                    case 3:
                        if (!token.marketCapUsd) return [3 /*break*/, 5];
                        return [4 /*yield*/, this.checkAndRecordMilestones(token.mint, token.marketCapUsd)];
                    case 4:
                        _a.sent();
                        _a.label = 5;
                    case 5: return [3 /*break*/, 7];
                    case 6:
                        error_2 = _a.sent();
                        logger_1.logger.error("Failed to process token ".concat(token.mint, ":"), error_2);
                        throw error_2;
                    case 7: return [2 /*return*/];
                }
            });
        });
    };
    /**
     * Upsert token data, handling initial vs current market cap logic
     */
    DataProcessor.prototype.upsertToken = function (token) {
        return __awaiter(this, void 0, void 0, function () {
            var existingToken, updateData, error, insertData, error, error_3;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        _a.trys.push([0, 6, , 7]);
                        return [4 /*yield*/, supabaseClient_1.supabase
                                .from(constants_1.DB_TABLES.TOKENS)
                                .select('mint, initial_market_cap_usd')
                                .eq('mint', token.mint)
                                .single()];
                    case 1:
                        existingToken = (_a.sent()).data;
                        if (!existingToken) return [3 /*break*/, 3];
                        updateData = {
                            name: token.name,
                            symbol: token.symbol,
                            description: token.description,
                            curated_description: token.curated_description,
                            current_market_cap_usd: token.current_market_cap_usd,
                            bonding_curve_address: token.bonding_curve_address,
                            associated_bonding_curve: token.associated_bonding_curve,
                            raydium_pool_address: token.raydium_pool_address,
                            is_nsfw: token.is_nsfw,
                            is_banned: token.is_banned,
                            updated_at: new Date().toISOString()
                        };
                        return [4 /*yield*/, supabaseClient_1.supabase
                                .from(constants_1.DB_TABLES.TOKENS)
                                .update(updateData)
                                .eq('mint', token.mint)];
                    case 2:
                        error = (_a.sent()).error;
                        if (error)
                            throw error;
                        logger_1.logger.database('UPDATE', constants_1.DB_TABLES.TOKENS, { mint: token.mint });
                        return [3 /*break*/, 5];
                    case 3:
                        insertData = __assign(__assign({}, token), { initial_market_cap_usd: token.current_market_cap_usd || 0, created_at: new Date().toISOString(), updated_at: new Date().toISOString() });
                        return [4 /*yield*/, supabaseClient_1.supabase
                                .from(constants_1.DB_TABLES.TOKENS)
                                .insert(insertData)];
                    case 4:
                        error = (_a.sent()).error;
                        if (error)
                            throw error;
                        logger_1.logger.database('INSERT', constants_1.DB_TABLES.TOKENS, { mint: token.mint });
                        _a.label = 5;
                    case 5: return [3 /*break*/, 7];
                    case 6:
                        error_3 = _a.sent();
                        logger_1.logger.error("Failed to upsert token ".concat(token.mint, ":"), error_3);
                        throw error_3;
                    case 7: return [2 /*return*/];
                }
            });
        });
    };
    /**
     * Add market data history entry
     */
    DataProcessor.prototype.addMarketDataHistory = function (token) {
        return __awaiter(this, void 0, void 0, function () {
            var historyData, error, error_4;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        _a.trys.push([0, 2, , 3]);
                        historyData = {
                            timestamp: new Date().toISOString(),
                            token_mint: token.mint,
                            market_cap_usd: token.marketCapUsd,
                            market_cap_sol: token.marketCapSol,
                            volume_24h: token.volume24h,
                            num_holders: token.numHolders
                        };
                        return [4 /*yield*/, supabaseClient_1.supabase
                                .from(constants_1.DB_TABLES.MARKET_DATA_HISTORY)
                                .insert(historyData)];
                    case 1:
                        error = (_a.sent()).error;
                        if (error)
                            throw error;
                        logger_1.logger.database('INSERT', constants_1.DB_TABLES.MARKET_DATA_HISTORY, { mint: token.mint });
                        return [3 /*break*/, 3];
                    case 2:
                        error_4 = _a.sent();
                        logger_1.logger.error("Failed to add market data history for ".concat(token.mint, ":"), error_4);
                        throw error_4;
                    case 3: return [2 /*return*/];
                }
            });
        });
    };
    /**
     * Check and record new milestones achieved
     */
    DataProcessor.prototype.checkAndRecordMilestones = function (mint, currentMarketCapUsd) {
        return __awaiter(this, void 0, void 0, function () {
            var tokenData, multiplier, milestones, achievedMilestones, achievedMilestoneIds_1, newMilestones, achievementData, error, error_5;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        _a.trys.push([0, 6, , 7]);
                        return [4 /*yield*/, supabaseClient_1.supabase
                                .from(constants_1.DB_TABLES.TOKENS)
                                .select('initial_market_cap_usd')
                                .eq('mint', mint)
                                .single()];
                    case 1:
                        tokenData = (_a.sent()).data;
                        if (!(tokenData === null || tokenData === void 0 ? void 0 : tokenData.initial_market_cap_usd) || tokenData.initial_market_cap_usd === 0) {
                            return [2 /*return*/]; // Can't calculate milestones without initial market cap
                        }
                        multiplier = currentMarketCapUsd / tokenData.initial_market_cap_usd;
                        return [4 /*yield*/, supabaseClient_1.supabase
                                .from(constants_1.DB_TABLES.MILESTONES)
                                .select('*')
                                .lte('multiplier', multiplier)];
                    case 2:
                        milestones = (_a.sent()).data;
                        if (!milestones || milestones.length === 0)
                            return [2 /*return*/];
                        return [4 /*yield*/, supabaseClient_1.supabase
                                .from(constants_1.DB_TABLES.ACHIEVED_MILESTONES)
                                .select('milestone_id')
                                .eq('token_mint', mint)];
                    case 3:
                        achievedMilestones = (_a.sent()).data;
                        achievedMilestoneIds_1 = new Set((achievedMilestones === null || achievedMilestones === void 0 ? void 0 : achievedMilestones.map(function (am) { return am.milestone_id; })) || []);
                        newMilestones = milestones.filter(function (m) { return !achievedMilestoneIds_1.has(m.id); });
                        if (!(newMilestones.length > 0)) return [3 /*break*/, 5];
                        achievementData = newMilestones.map(function (milestone) { return ({
                            token_mint: mint,
                            milestone_id: milestone.id,
                            achieved_at_timestamp: new Date().toISOString(),
                            market_cap_at_milestone: currentMarketCapUsd
                        }); });
                        return [4 /*yield*/, supabaseClient_1.supabase
                                .from(constants_1.DB_TABLES.ACHIEVED_MILESTONES)
                                .insert(achievementData)];
                    case 4:
                        error = (_a.sent()).error;
                        if (error)
                            throw error;
                        logger_1.logger.database('INSERT', constants_1.DB_TABLES.ACHIEVED_MILESTONES, {
                            mint: mint,
                            newMilestones: newMilestones.map(function (m) { return "".concat(m.multiplier, "x"); })
                        });
                        _a.label = 5;
                    case 5: return [3 /*break*/, 7];
                    case 6:
                        error_5 = _a.sent();
                        logger_1.logger.error("Failed to check milestones for ".concat(mint, ":"), error_5);
                        throw error_5;
                    case 7: return [2 /*return*/];
                }
            });
        });
    };
    /**
     * Update category associations for a batch of tokens
     */
    DataProcessor.prototype.updateCategoryAssociations = function (tokenMints, categoryId) {
        return __awaiter(this, void 0, void 0, function () {
            var deleteError, deleteError, categoryData, upsertError, error_6;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        _a.trys.push([0, 6, , 7]);
                        if (!(tokenMints.length > 0)) return [3 /*break*/, 2];
                        return [4 /*yield*/, supabaseClient_1.supabase
                                .from(constants_1.DB_TABLES.TOKEN_CATEGORIES)
                                .delete()
                                .eq('category_id', categoryId)
                                .not('token_mint', 'in', "(".concat(tokenMints.map(function (mint) { return "\"".concat(mint, "\""); }).join(','), ")"))];
                    case 1:
                        deleteError = (_a.sent()).error;
                        if (deleteError)
                            throw deleteError;
                        return [3 /*break*/, 4];
                    case 2: return [4 /*yield*/, supabaseClient_1.supabase
                            .from(constants_1.DB_TABLES.TOKEN_CATEGORIES)
                            .delete()
                            .eq('category_id', categoryId)];
                    case 3:
                        deleteError = (_a.sent()).error;
                        if (deleteError)
                            throw deleteError;
                        _a.label = 4;
                    case 4:
                        categoryData = tokenMints.map(function (mint) { return ({
                            token_mint: mint,
                            category_id: categoryId,
                            created_at: new Date().toISOString()
                        }); });
                        return [4 /*yield*/, supabaseClient_1.supabase
                                .from(constants_1.DB_TABLES.TOKEN_CATEGORIES)
                                .upsert(categoryData, { onConflict: 'token_mint,category_id' })];
                    case 5:
                        upsertError = (_a.sent()).error;
                        if (upsertError)
                            throw upsertError;
                        logger_1.logger.database('UPSERT', constants_1.DB_TABLES.TOKEN_CATEGORIES, {
                            categoryId: categoryId,
                            tokenCount: tokenMints.length
                        });
                        return [3 /*break*/, 7];
                    case 6:
                        error_6 = _a.sent();
                        logger_1.logger.error("Failed to update category associations for category ".concat(categoryId, ":"), error_6);
                        throw error_6;
                    case 7: return [2 /*return*/];
                }
            });
        });
    };
    /**
     * Get category by slug
     */
    DataProcessor.prototype.getCategoryBySlug = function (slug) {
        return __awaiter(this, void 0, void 0, function () {
            var _a, data, error, error_7;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        _b.trys.push([0, 2, , 3]);
                        return [4 /*yield*/, supabaseClient_1.supabase
                                .from(constants_1.DB_TABLES.CATEGORIES)
                                .select('*')
                                .eq('slug', slug)
                                .single()];
                    case 1:
                        _a = _b.sent(), data = _a.data, error = _a.error;
                        if (error) {
                            if (error.code === 'PGRST116') { // No rows returned
                                return [2 /*return*/, null];
                            }
                            throw error;
                        }
                        return [2 /*return*/, data];
                    case 2:
                        error_7 = _b.sent();
                        logger_1.logger.error("Failed to get category by slug ".concat(slug, ":"), error_7);
                        throw error_7;
                    case 3: return [2 /*return*/];
                }
            });
        });
    };
    /**
     * Convert UnifiedToken to DatabaseToken format
     */
    DataProcessor.prototype.convertToDbFormat = function (token) {
        return {
            mint: token.mint,
            name: token.name,
            symbol: token.symbol,
            creator: token.creator,
            description: token.description,
            curated_description: token.curatedDescription,
            image_url: token.imageUrl,
            video_url: token.videoUrl,
            banner_url: token.bannerUrl,
            website_url: token.websiteUrl,
            twitter_url: token.twitterUrl,
            telegram_url: token.telegramUrl,
            metadata_url: token.metadataUrl,
            initial_market_cap_usd: 0, // Will be set during upsert logic
            current_market_cap_usd: token.marketCapUsd,
            bonding_curve_address: token.bondingCurveAddress,
            associated_bonding_curve: token.associatedBondingCurve,
            raydium_pool_address: token.raydiumPoolAddress,
            is_nsfw: token.isNsfw,
            is_banned: token.isBanned
        };
    };
    return DataProcessor;
}());
exports.DataProcessor = DataProcessor;
exports.dataProcessor = new DataProcessor();
