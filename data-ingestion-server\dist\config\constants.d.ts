import { CategoryConfig } from '../types/api';
export declare const API_URLS: {
    readonly FRONTEND_V3: "https://frontend-api-v3.pump.fun";
    readonly ADVANCED_V2: "https://advanced-api-v2.pump.fun";
    readonly LEGACY: "https://pump.fun/api";
};
export declare const CATEGORY_CONFIGS: CategoryConfig[];
export declare const API_PARAMS: {
    readonly LIMIT: number;
    readonly OFFSET: number;
    readonly INCLUDE_NSFW: boolean;
};
export declare const MILESTONE_MULTIPLIERS: number[];
export declare const DB_TABLES: {
    readonly TOKENS: "tokens";
    readonly CATEGORIES: "categories";
    readonly TOKEN_CATEGORIES: "token_categories";
    readonly MARKET_DATA_HISTORY: "market_data_history";
    readonly MILESTONES: "milestones";
    readonly ACHIEVED_MILESTONES: "achieved_milestones";
};
export declare const RETRY_CONFIG: {
    readonly MAX_RETRIES: 3;
    readonly RETRY_DELAY: 1000;
    readonly BACKOFF_MULTIPLIER: 2;
};
export declare const LOG_LEVELS: {
    readonly ERROR: "error";
    readonly WARN: "warn";
    readonly INFO: "info";
    readonly DEBUG: "debug";
};
//# sourceMappingURL=constants.d.ts.map