import { getSupabase } from './supabaseClient';
import { UnifiedToken, DatabaseToken, Category, MarketDataHistory, Milestone, AchievedMilestone } from '../types/unified';
import { DB_TABLES, MILESTONE_MULTIPLIERS } from '../config/constants';
import { logger } from '../utils/logger';

/**
 * Handles all database operations for token data processing
 */
export class DataProcessor {
  /**
   * Main function to process and upsert a batch of tokens for a specific category
   */
  async processAndUpsertData(tokens: UnifiedToken[], categorySlug: string): Promise<void> {
    if (tokens.length === 0) {
      logger.warn(`No tokens to process for category: ${categorySlug}`);
      return;
    }

    logger.polling(categorySlug, 'Processing tokens', { count: tokens.length });

    try {
      // Get category ID
      const category = await this.getCategoryBySlug(categorySlug);
      if (!category) {
        throw new Error(`Category not found: ${categorySlug}`);
      }

      // Process each token
      for (const token of tokens) {
        await this.processToken(token, category.id);
      }

      // Update category associations
      await this.updateCategoryAssociations(tokens.map(t => t.mint), category.id);

      logger.polling(categorySlug, 'Successfully processed tokens', { count: tokens.length });
    } catch (error) {
      logger.error(`Failed to process tokens for category ${categorySlug}:`, {
        error: error instanceof Error ? error.message : error,
        stack: error instanceof Error ? error.stack : undefined,
        categorySlug,
        tokenCount: tokens.length
      });
      throw error;
    }
  }

  /**
   * Process a single token: upsert, add market history, check milestones
   */
  private async processToken(token: UnifiedToken, categoryId: number): Promise<void> {
    try {
      // Convert to database format
      const dbToken = this.convertToDbFormat(token);
      
      // Upsert token
      await this.upsertToken(dbToken);
      
      // Add market data history
      if (token.marketCapUsd || token.marketCapSol || token.volume24h) {
        await this.addMarketDataHistory(token);
      }
      
      // Check and record milestones
      if (token.marketCapUsd) {
        await this.checkAndRecordMilestones(token.mint, token.marketCapUsd);
      }
      
    } catch (error) {
      logger.error(`Failed to process token ${token.mint}:`, error);
      throw error;
    }
  }

  /**
   * Upsert token data, handling initial vs current market cap logic
   */
  private async upsertToken(token: Partial<DatabaseToken> & { mint: string }): Promise<void> {
    try {
      // Check if token already exists
      const { data: existingToken } = await getSupabase()
        .from(DB_TABLES.TOKENS)
        .select('mint, initial_market_cap_usd')
        .eq('mint', token.mint)
        .single();

      if (existingToken) {
        // Token exists - update all available fields (preserving existing data for missing fields)
        const updateData: Partial<DatabaseToken> = {
          ...token, // Include all available fields from the converted token
          updated_at: new Date().toISOString()
        };

        // Remove fields that shouldn't be updated on existing tokens
        delete updateData.mint; // Primary key shouldn't be updated
        delete updateData.initial_market_cap_usd; // Don't overwrite initial market cap
        delete updateData.created_at; // Don't overwrite creation timestamp

        const { error } = await getSupabase()
          .from(DB_TABLES.TOKENS)
          .update(updateData)
          .eq('mint', token.mint);

        if (error) throw error;

        logger.database('UPDATE', DB_TABLES.TOKENS, { mint: token.mint });
      } else {
        // New token - insert with initial market cap
        const insertData: DatabaseToken = {
          ...token,
          initial_market_cap_usd: token.current_market_cap_usd || 0,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        } as DatabaseToken;

        const { error } = await getSupabase()
          .from(DB_TABLES.TOKENS)
          .insert(insertData);

        if (error) throw error;

        logger.database('INSERT', DB_TABLES.TOKENS, { mint: token.mint });
      }
    } catch (error) {
      logger.error(`Failed to upsert token ${token.mint}:`, error);
      throw error;
    }
  }

  /**
   * Add market data history entry
   */
  private async addMarketDataHistory(token: UnifiedToken): Promise<void> {
    try {
      const historyData: MarketDataHistory = {
        timestamp: new Date().toISOString(),
        token_mint: token.mint,
        market_cap_usd: token.marketCapUsd,
        market_cap_sol: token.marketCapSol,
        volume_24h: token.volume24h,
        num_holders: token.numHolders
      };

      // Add enrichment transaction data if available
      const enrichmentTxns = (token as any).enrichmentTxns;
      if (enrichmentTxns) {
        historyData.txns_m5_buys = enrichmentTxns.txnsM5Buys;
        historyData.txns_m5_sells = enrichmentTxns.txnsM5Sells;
        historyData.txns_h1_buys = enrichmentTxns.txnsH1Buys;
        historyData.txns_h1_sells = enrichmentTxns.txnsH1Sells;
        historyData.txns_h6_buys = enrichmentTxns.txnsH6Buys;
        historyData.txns_h6_sells = enrichmentTxns.txnsH6Sells;
        historyData.txns_h24_buys = enrichmentTxns.txnsH24Buys;
        historyData.txns_h24_sells = enrichmentTxns.txnsH24Sells;
      }

      const { error } = await getSupabase()
        .from(DB_TABLES.MARKET_DATA_HISTORY)
        .insert(historyData);

      if (error) throw error;

      logger.database('INSERT', DB_TABLES.MARKET_DATA_HISTORY, { mint: token.mint });
    } catch (error) {
      logger.error(`Failed to add market data history for ${token.mint}:`, error);
      throw error;
    }
  }

  /**
   * Check and record new milestones achieved
   */
  private async checkAndRecordMilestones(mint: string, currentMarketCapUsd: number): Promise<void> {
    try {
      // Get initial market cap
      const { data: tokenData } = await getSupabase()
        .from(DB_TABLES.TOKENS)
        .select('initial_market_cap_usd')
        .eq('mint', mint)
        .single();

      if (!tokenData?.initial_market_cap_usd || tokenData.initial_market_cap_usd === 0) {
        return; // Can't calculate milestones without initial market cap
      }

      const multiplier = currentMarketCapUsd / tokenData.initial_market_cap_usd;
      
      // Get milestones that should be achieved
      const { data: milestones } = await getSupabase()
        .from(DB_TABLES.MILESTONES)
        .select('*')
        .lte('multiplier', multiplier);

      if (!milestones || milestones.length === 0) return;

      // Get already achieved milestones
      const { data: achievedMilestones } = await getSupabase()
        .from(DB_TABLES.ACHIEVED_MILESTONES)
        .select('milestone_id')
        .eq('token_mint', mint);

      const achievedMilestoneIds = new Set(achievedMilestones?.map((am: any) => am.milestone_id) || []);

      // Find new milestones to record
      const newMilestones = milestones.filter((m: any) => !achievedMilestoneIds.has(m.id));

      if (newMilestones.length > 0) {
        const achievementData: AchievedMilestone[] = newMilestones.map((milestone: any) => ({
          token_mint: mint,
          milestone_id: milestone.id,
          achieved_at_timestamp: new Date().toISOString(),
          market_cap_at_milestone: currentMarketCapUsd
        }));

        const { error } = await getSupabase()
          .from(DB_TABLES.ACHIEVED_MILESTONES)
          .insert(achievementData);

        if (error) throw error;

        logger.database('INSERT', DB_TABLES.ACHIEVED_MILESTONES, { 
          mint, 
          newMilestones: newMilestones.map((m: any) => `${m.multiplier}x`) 
        });
      }
    } catch (error) {
      logger.error(`Failed to check milestones for ${mint}:`, error);
      throw error;
    }
  }

  /**
   * Update category associations for a batch of tokens
   */
  private async updateCategoryAssociations(tokenMints: string[], categoryId: number): Promise<void> {
    try {
      logger.debug('Updating category associations', {
        categoryId,
        tokenCount: tokenMints.length,
        sampleMints: tokenMints.slice(0, 3)
      });
      // Remove all existing associations for this category first
      // This is simpler and more reliable than trying to delete only stale ones
      const { error: deleteError } = await getSupabase()
        .from(DB_TABLES.TOKEN_CATEGORIES)
        .delete()
        .eq('category_id', categoryId);

      if (deleteError) {
        logger.error('Failed to delete existing category associations:', deleteError);
        throw deleteError;
      }

      // Add new category associations (using upsert to handle duplicates)
      const categoryData = tokenMints.map(mint => ({
        token_mint: mint,
        category_id: categoryId,
        created_at: new Date().toISOString()
      }));

      const { error: upsertError } = await getSupabase()
        .from(DB_TABLES.TOKEN_CATEGORIES)
        .insert(categoryData);

      if (upsertError) {
        logger.error('Failed to upsert category associations:', {
          error: upsertError,
          categoryId,
          sampleData: categoryData.slice(0, 3)
        });
        throw upsertError;
      }

      logger.database('UPSERT', DB_TABLES.TOKEN_CATEGORIES, { 
        categoryId, 
        tokenCount: tokenMints.length 
      });
    } catch (error) {
      logger.error(`Failed to update category associations for category ${categoryId}:`, {
        error: error instanceof Error ? error.message : error,
        stack: error instanceof Error ? error.stack : undefined,
        categoryId,
        tokenCount: tokenMints.length,
        sampleMints: tokenMints.slice(0, 3)
      });
      throw error;
    }
  }

  /**
   * Get category by slug
   */
  private async getCategoryBySlug(slug: string): Promise<Category | null> {
    try {
      const { data, error } = await getSupabase()
        .from(DB_TABLES.CATEGORIES)
        .select('*')
        .eq('slug', slug)
        .single();

      if (error) {
        if (error.code === 'PGRST116') { // No rows returned
          return null;
        }
        throw error;
      }

      return data;
    } catch (error) {
      logger.error(`Failed to get category by slug ${slug}:`, error);
      throw error;
    }
  }

  /**
   * Convert UnifiedToken to DatabaseToken format
   */
  private convertToDbFormat(token: UnifiedToken): Partial<DatabaseToken> & { mint: string } {
    // Only include fields that have values - this allows for proper upsert behavior
    const dbToken: Partial<DatabaseToken> & { mint: string } = {
      // Core identifiers (always required)
      mint: token.mint,
      name: token.name,
      symbol: token.symbol,
      creator: token.creator,

      // Metadata (only if available)
      ...(token.description !== undefined && { description: token.description }),
      ...(token.curatedDescription !== undefined && { curated_description: token.curatedDescription }),
      ...(token.imageUrl !== undefined && { image_url: token.imageUrl }),
      ...(token.videoUrl !== undefined && { video_url: token.videoUrl }),
      ...(token.bannerUrl !== undefined && { banner_url: token.bannerUrl }),
      ...(token.websiteUrl !== undefined && { website_url: token.websiteUrl }),
      ...(token.twitterUrl !== undefined && { twitter_url: token.twitterUrl }),
      ...(token.telegramUrl !== undefined && { telegram_url: token.telegramUrl }),
      ...(token.metadataUrl !== undefined && { metadata_url: token.metadataUrl }),
      ...(token.dexscreenerUrl !== undefined && { dexscreener_url: token.dexscreenerUrl }),

      // Market data (only if available)
      ...(token.marketCapUsd !== undefined && { current_market_cap_usd: token.marketCapUsd }),
      ...(token.fdv !== undefined && { fdv: token.fdv }),
      ...(token.priceUsd !== undefined && { price_usd: token.priceUsd }),
      ...(token.priceNative !== undefined && { price_native: token.priceNative }),
      ...(token.priceChangeM5 !== undefined && { price_change_m5: token.priceChangeM5 }),
      ...(token.priceChangeH1 !== undefined && { price_change_h1: token.priceChangeH1 }),
      ...(token.priceChangeH6 !== undefined && { price_change_h6: token.priceChangeH6 }),
      ...(token.priceChangeH24 !== undefined && { price_change_h24: token.priceChangeH24 }),

      // Pump.fun specific (only if available)
      ...(token.bondingCurveAddress !== undefined && { bonding_curve_address: token.bondingCurveAddress }),
      ...(token.associatedBondingCurve !== undefined && { associated_bonding_curve: token.associatedBondingCurve }),
      ...(token.raydiumPoolAddress !== undefined && { raydium_pool_address: token.raydiumPoolAddress }),
      ...(token.graduationTimestamp !== undefined && {
        graduation_timestamp: new Date(token.graduationTimestamp).toISOString()
      }),

      // Flags (only if available)
      ...(token.isNsfw !== undefined && { is_nsfw: token.isNsfw }),
      ...(token.isBanned !== undefined && { is_banned: token.isBanned }),

      // Always set initial market cap to 0 for new tokens (will be overridden in upsert logic)
      initial_market_cap_usd: 0
    };

    return dbToken;
  }
}

export const dataProcessor = new DataProcessor();
