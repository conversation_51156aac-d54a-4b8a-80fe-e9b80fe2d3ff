import { SupabaseClient } from '@supabase/supabase-js';
declare class SupabaseService {
    private static instance;
    private client;
    private constructor();
    static getInstance(): SupabaseService;
    getClient(): SupabaseClient;
    testConnection(): Promise<boolean>;
}
export declare function getSupabaseService(): SupabaseService;
export declare function getSupabase(): SupabaseClient<any, "public", any>;
export {};
//# sourceMappingURL=supabaseClient.d.ts.map