{"version": 3, "file": "normalizer.js", "sourceRoot": "", "sources": ["../../src/utils/normalizer.ts"], "names": [], "mappings": ";;;AACA,sCAAsF;AACtF,qCAAkC;AAKlC,MAAa,eAAe;IAI1B,MAAM,CAAC,cAAc,CAAC,QAAa,EAAE,OAAgB;QACnD,IAAI,CAAC;YAEH,eAAM,CAAC,KAAK,CAAC,oBAAoB,EAAE;gBACjC,OAAO;gBACP,SAAS,EAAE,MAAM,CAAC,IAAI,CAAC,QAAQ,IAAI,EAAE,CAAC;gBACtC,UAAU,EAAE;oBACV,IAAI,EAAE,QAAQ,EAAE,IAAI,IAAI,QAAQ,EAAE,QAAQ;oBAC1C,IAAI,EAAE,QAAQ,EAAE,IAAI;oBACpB,MAAM,EAAE,QAAQ,EAAE,MAAM,IAAI,QAAQ,EAAE,MAAM;oBAC5C,IAAI,EAAE,QAAQ,EAAE,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,SAAS;iBAC9D;aACF,CAAC,CAAC;YAEH,QAAQ,OAAO,EAAE,CAAC;gBAChB,KAAK,aAAO,CAAC,WAAW;oBACtB,OAAO,IAAI,CAAC,wBAAwB,CAAC,QAA2B,CAAC,CAAC;gBACpE,KAAK,aAAO,CAAC,WAAW;oBACtB,OAAO,IAAI,CAAC,wBAAwB,CAAC,QAAQ,CAAC,CAAC;gBACjD,KAAK,aAAO,CAAC,MAAM;oBACjB,OAAO,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,CAAC;gBAC7C;oBACE,MAAM,IAAI,KAAK,CAAC,qBAAqB,OAAO,EAAE,CAAC,CAAC;YACpD,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,6BAA6B,EAAE;gBAC1C,OAAO;gBACP,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK;gBACrD,SAAS,EAAE,MAAM,CAAC,IAAI,CAAC,QAAQ,IAAI,EAAE,CAAC;gBACtC,cAAc,EAAE,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC;aAC3D,CAAC,CAAC;YACH,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAKO,MAAM,CAAC,wBAAwB,CAAC,KAAsB;QAC5D,OAAO;YAEL,IAAI,EAAE,KAAK,CAAC,IAAI,IAAI,EAAE;YACtB,IAAI,EAAE,KAAK,CAAC,IAAI,IAAI,EAAE;YACtB,MAAM,EAAE,KAAK,CAAC,MAAM,IAAI,EAAE;YAC1B,OAAO,EAAE,KAAK,CAAC,OAAO,IAAI,EAAE;YAG5B,WAAW,EAAE,KAAK,CAAC,WAAW,IAAI,EAAE;YACpC,QAAQ,EAAE,KAAK,CAAC,SAAS;YACzB,WAAW,EAAE,KAAK,CAAC,YAAY;YAC/B,UAAU,EAAE,KAAK,CAAC,OAAO;YACzB,UAAU,EAAE,KAAK,CAAC,OAAO;YACzB,WAAW,EAAE,KAAK,CAAC,QAAQ;YAG3B,YAAY,EAAE,KAAK,CAAC,cAAc;YAClC,YAAY,EAAE,KAAK,CAAC,UAAU;YAC9B,SAAS,EAAE,KAAK,CAAC,UAAU;YAG3B,iBAAiB,EAAE,KAAK,CAAC,iBAAiB;YAC1C,UAAU,EAAE,KAAK,CAAC,QAAQ;YAC1B,MAAM,EAAE,KAAK,CAAC,iBAAiB;YAG/B,mBAAmB,EAAE,KAAK,CAAC,aAAa;YACxC,sBAAsB,EAAE,KAAK,CAAC,wBAAwB;YACtD,kBAAkB,EAAE,KAAK,CAAC,YAAY;YAGtC,MAAM,EAAE,KAAK,CAAC,IAAI,IAAI,KAAK;YAC3B,SAAS,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,IAAI,KAAK,CAAC,OAAO,IAAI,KAAK,CAAC,QAAQ,CAAC;SAChE,CAAC;IACJ,CAAC;IAKO,MAAM,CAAC,wBAAwB,CAAC,KAAU;QAEhD,OAAO;YAEL,IAAI,EAAE,KAAK,CAAC,IAAI,IAAI,KAAK,CAAC,QAAQ,IAAI,KAAK,CAAC,OAAO,IAAI,EAAE;YACzD,IAAI,EAAE,KAAK,CAAC,IAAI,IAAI,KAAK,CAAC,UAAU,IAAI,EAAE;YAC1C,MAAM,EAAE,KAAK,CAAC,MAAM,IAAI,KAAK,CAAC,MAAM,IAAI,KAAK,CAAC,YAAY,IAAI,EAAE;YAChE,OAAO,EAAE,KAAK,CAAC,OAAO,IAAI,KAAK,CAAC,GAAG,IAAI,KAAK,CAAC,SAAS,IAAI,EAAE;YAG5D,WAAW,EAAE,KAAK,CAAC,WAAW,IAAI,EAAE;YACpC,QAAQ,EAAE,KAAK,CAAC,SAAS,IAAI,KAAK,CAAC,QAAQ,IAAI,KAAK,CAAC,KAAK;YAC1D,QAAQ,EAAE,KAAK,CAAC,SAAS,IAAI,KAAK,CAAC,QAAQ;YAC3C,SAAS,EAAE,KAAK,CAAC,UAAU,IAAI,KAAK,CAAC,SAAS;YAC9C,WAAW,EAAE,KAAK,CAAC,YAAY,IAAI,KAAK,CAAC,WAAW;YACpD,UAAU,EAAE,KAAK,CAAC,OAAO,IAAI,KAAK,CAAC,WAAW;YAC9C,UAAU,EAAE,KAAK,CAAC,OAAO,IAAI,KAAK,CAAC,WAAW;YAC9C,WAAW,EAAE,KAAK,CAAC,QAAQ,IAAI,KAAK,CAAC,YAAY;YAGjD,YAAY,EAAE,KAAK,CAAC,cAAc,IAAI,KAAK,CAAC,YAAY;YACxD,YAAY,EAAE,KAAK,CAAC,UAAU,IAAI,KAAK,CAAC,SAAS;YACjD,SAAS,EAAE,KAAK,CAAC,UAAU,IAAI,KAAK,CAAC,MAAM;YAC3C,WAAW,EAAE,KAAK,CAAC,YAAY,IAAI,KAAK,CAAC,WAAW;YAGpD,iBAAiB,EAAE,KAAK,CAAC,iBAAiB,IAAI,KAAK,CAAC,YAAY;YAChE,mBAAmB,EAAE,KAAK,CAAC,oBAAoB,IAAI,KAAK,CAAC,cAAc;YACvE,UAAU,EAAE,KAAK,CAAC,QAAQ;YAC1B,MAAM,EAAE,KAAK,CAAC,iBAAiB;YAG/B,mBAAmB,EAAE,KAAK,CAAC,aAAa,IAAI,KAAK,CAAC,mBAAmB;YACrE,sBAAsB,EAAE,KAAK,CAAC,wBAAwB,IAAI,KAAK,CAAC,sBAAsB;YACtF,kBAAkB,EAAE,KAAK,CAAC,YAAY,IAAI,KAAK,CAAC,WAAW;YAC3D,kBAAkB,EAAE,KAAK,CAAC,oBAAoB,IAAI,KAAK,CAAC,kBAAkB;YAC1E,oBAAoB,EAAE,KAAK,CAAC,sBAAsB,IAAI,KAAK,CAAC,oBAAoB;YAGhF,UAAU,EAAE,KAAK,CAAC,WAAW,IAAI,KAAK,CAAC,UAAU;YACjD,kBAAkB,EAAE,KAAK,CAAC,UAAU,IAAI,KAAK,CAAC,kBAAkB;YAChE,MAAM,EAAE,KAAK,CAAC,IAAI,IAAI,KAAK;YAC3B,SAAS,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,IAAI,KAAK,CAAC,OAAO,IAAI,KAAK,CAAC,QAAQ,CAAC;SAChE,CAAC;IACJ,CAAC;IAKO,MAAM,CAAC,oBAAoB,CAAC,KAAU;QAE5C,MAAM,QAAQ,GAAG,KAAK,CAAC,IAAI,IAAI,KAAK,CAAC;QACrC,MAAM,OAAO,GAAkB,KAAK,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,MAAW,EAAE,EAAE,CAAC,CAAC;YAClE,QAAQ,EAAE,MAAM,CAAC,QAAQ;YACzB,oBAAoB,EAAE,MAAM,CAAC,oBAAoB;YACjD,eAAe,EAAE,MAAM,CAAC,eAAe;YACvC,QAAQ,EAAE,MAAM,CAAC,QAAQ;SAC1B,CAAC,CAAC,IAAI,EAAE,CAAC;QAEV,OAAO;YAEL,IAAI,EAAE,QAAQ,CAAC,IAAI,IAAI,QAAQ,CAAC,QAAQ,IAAI,KAAK,CAAC,QAAQ,IAAI,EAAE;YAChE,IAAI,EAAE,QAAQ,CAAC,IAAI,IAAI,KAAK,CAAC,IAAI,IAAI,EAAE;YACvC,MAAM,EAAE,QAAQ,CAAC,MAAM,IAAI,QAAQ,CAAC,MAAM,IAAI,KAAK,CAAC,MAAM,IAAI,EAAE;YAChE,OAAO,EAAE,QAAQ,CAAC,OAAO,IAAI,QAAQ,CAAC,GAAG,IAAI,KAAK,CAAC,GAAG,IAAI,EAAE;YAG5D,WAAW,EAAE,QAAQ,CAAC,WAAW,IAAI,KAAK,CAAC,WAAW,IAAI,EAAE;YAC5D,kBAAkB,EAAE,KAAK,CAAC,kBAAkB,IAAI,KAAK,CAAC,WAAW;YACjE,QAAQ,EAAE,QAAQ,CAAC,SAAS,IAAI,QAAQ,CAAC,QAAQ,IAAI,KAAK,CAAC,QAAQ;YACnE,QAAQ,EAAE,QAAQ,CAAC,SAAS,IAAI,QAAQ,CAAC,QAAQ,IAAI,KAAK,CAAC,QAAQ;YACnE,SAAS,EAAE,QAAQ,CAAC,UAAU,IAAI,QAAQ,CAAC,SAAS,IAAI,KAAK,CAAC,SAAS;YACvE,WAAW,EAAE,QAAQ,CAAC,YAAY,IAAI,QAAQ,CAAC,WAAW,IAAI,KAAK,CAAC,WAAW;YAC/E,UAAU,EAAE,QAAQ,CAAC,OAAO,IAAI,KAAK,CAAC,OAAO;YAC7C,UAAU,EAAE,QAAQ,CAAC,OAAO,IAAI,KAAK,CAAC,OAAO;YAC7C,WAAW,EAAE,QAAQ,CAAC,QAAQ,IAAI,KAAK,CAAC,QAAQ;YAGhD,YAAY,EAAE,KAAK,CAAC,SAAS,IAAI,QAAQ,CAAC,UAAU;YACpD,uBAAuB,EAAE,KAAK,CAAC,oBAAoB;YACnD,SAAS,EAAE,KAAK,CAAC,MAAM,IAAI,QAAQ,CAAC,UAAU;YAC9C,WAAW,EAAE,KAAK,CAAC,WAAW,IAAI,QAAQ,CAAC,YAAY;YACvD,kBAAkB,EAAE,KAAK,CAAC,kBAAkB;YAG5C,UAAU,EAAE,KAAK,CAAC,OAAO,EAAE,MAAM;YACjC,oBAAoB,EAAE,KAAK,CAAC,oBAAoB;YAChD,qBAAqB,EAAE,KAAK,CAAC,qBAAqB;YAClD,eAAe,EAAE,KAAK,CAAC,eAAe;YACtC,gBAAgB,EAAE,KAAK,CAAC,gBAAgB;YACxC,iBAAiB,EAAE,KAAK,CAAC,YAAY;YACrC,OAAO,EAAE,OAAO;YAGhB,WAAW,EAAE,KAAK,CAAC,WAAW;YAC9B,qBAAqB,EAAE,KAAK,CAAC,qBAAqB;YAGlD,iBAAiB,EAAE,KAAK,CAAC,YAAY,IAAI,QAAQ,CAAC,iBAAiB;YACnE,mBAAmB,EAAE,KAAK,CAAC,cAAc,IAAI,QAAQ,CAAC,oBAAoB;YAC1E,oBAAoB,EAAE,KAAK,CAAC,oBAAoB;YAChD,UAAU,EAAE,KAAK,CAAC,QAAQ,IAAI,QAAQ,CAAC,QAAQ;YAG/C,kBAAkB,EAAE,KAAK,CAAC,WAAW,IAAI,QAAQ,CAAC,YAAY;YAC9D,kBAAkB,EAAE,KAAK,CAAC,kBAAkB,IAAI,QAAQ,CAAC,oBAAoB;YAC7E,oBAAoB,EAAE,KAAK,CAAC,oBAAoB,IAAI,QAAQ,CAAC,sBAAsB;YAGnF,UAAU,EAAE,KAAK,CAAC,UAAU,IAAI,QAAQ,CAAC,WAAW;YACpD,kBAAkB,EAAE,KAAK,CAAC,kBAAkB,IAAI,QAAQ,CAAC,UAAU;YACnE,SAAS,EAAE,CAAC,CAAC,CAAC,QAAQ,CAAC,OAAO,IAAI,KAAK,CAAC,OAAO,IAAI,QAAQ,CAAC,OAAO,IAAI,KAAK,CAAC,OAAO,IAAI,QAAQ,CAAC,QAAQ,IAAI,KAAK,CAAC,QAAQ,CAAC;YAC5H,MAAM,EAAE,KAAK,CAAC,IAAI,IAAI,QAAQ,CAAC,IAAI,IAAI,KAAK;YAC5C,iBAAiB,EAAE,KAAK,CAAC,iBAAiB;SAC3C,CAAC;IACJ,CAAC;IAKD,MAAM,CAAC,aAAa,CAAC,KAAmB;QAEtC,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC;YAChB,eAAM,CAAC,IAAI,CAAC,+CAA+C,EAAE;gBAC3D,IAAI,EAAE,KAAK,CAAC,IAAI;gBAChB,IAAI,EAAE,KAAK,CAAC,IAAI;gBAChB,MAAM,EAAE,KAAK,CAAC,MAAM;gBACpB,OAAO,EAAE,CAAC,CAAC,KAAK,CAAC,IAAI;gBACrB,SAAS,EAAE,CAAC,CAAC,KAAK,CAAC,MAAM;aAC1B,CAAC,CAAC;YACH,OAAO,KAAK,CAAC;QACf,CAAC;QAGD,IAAI,CAAC,KAAK,CAAC,IAAI,IAAI,KAAK,CAAC,MAAM,EAAE,CAAC;YAChC,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,MAAM,CAAC;YAC1B,eAAM,CAAC,KAAK,CAAC,gCAAgC,EAAE,EAAE,IAAI,EAAE,KAAK,CAAC,IAAI,EAAE,MAAM,EAAE,KAAK,CAAC,MAAM,EAAE,CAAC,CAAC;QAC7F,CAAC;QAGD,IAAI,CAAC,KAAK,CAAC,MAAM,IAAI,KAAK,CAAC,IAAI,EAAE,CAAC;YAChC,KAAK,CAAC,MAAM,GAAG,KAAK,CAAC,IAAI,CAAC;YAC1B,eAAM,CAAC,KAAK,CAAC,gCAAgC,EAAE,EAAE,IAAI,EAAE,KAAK,CAAC,IAAI,EAAE,IAAI,EAAE,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC;QACzF,CAAC;QAGD,IAAI,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,EAAE,CAAC;YAClD,eAAM,CAAC,IAAI,CAAC,kDAAkD,EAAE;gBAC9D,IAAI,EAAE,KAAK,CAAC,IAAI;gBAChB,IAAI,EAAE,KAAK,CAAC,IAAI;gBAChB,MAAM,EAAE,KAAK,CAAC,MAAM;aACrB,CAAC,CAAC;YACH,OAAO,KAAK,CAAC;QACf,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;CACF;AA/OD,0CA+OC;AAEY,QAAA,cAAc,GAAG,eAAe,CAAC,cAAc,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;AACtE,QAAA,aAAa,GAAG,eAAe,CAAC,aAAa,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC"}