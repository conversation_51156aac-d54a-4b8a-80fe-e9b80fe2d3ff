"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.validateToken = exports.normalizeToken = exports.TokenNormalizer = void 0;
const api_1 = require("../types/api");
const logger_1 = require("./logger");
class TokenNormalizer {
    static normalizeToken(rawToken, apiType) {
        try {
            logger_1.logger.debug('Normalizing token:', {
                apiType,
                tokenKeys: Object.keys(rawToken || {}),
                sampleData: {
                    mint: rawToken?.mint || rawToken?.coinMint,
                    name: rawToken?.name,
                    symbol: rawToken?.symbol || rawToken?.ticker,
                    coin: rawToken?.coin ? Object.keys(rawToken.coin) : undefined
                }
            });
            switch (apiType) {
                case api_1.ApiType.FRONTEND_V3:
                    return this.normalizeFrontendV3Token(rawToken);
                case api_1.ApiType.ADVANCED_V2:
                    return this.normalizeAdvancedV2Token(rawToken);
                case api_1.ApiType.LEGACY:
                    return this.normalizeLegacyToken(rawToken);
                default:
                    throw new Error(`Unknown API type: ${apiType}`);
            }
        }
        catch (error) {
            logger_1.logger.error('Token normalization failed:', {
                apiType,
                error: error instanceof Error ? error.message : error,
                tokenKeys: Object.keys(rawToken || {}),
                rawTokenSample: JSON.stringify(rawToken).substring(0, 500)
            });
            throw error;
        }
    }
    static normalizeFrontendV3Token(token) {
        return {
            mint: token.mint || '',
            name: token.name || '',
            symbol: token.symbol || '',
            creator: token.creator || '',
            description: token.description || '',
            imageUrl: token.image_uri,
            metadataUrl: token.metadata_uri,
            websiteUrl: token.website,
            twitterUrl: token.twitter,
            telegramUrl: token.telegram,
            marketCapUsd: token.usd_market_cap,
            marketCapSol: token.market_cap,
            volume24h: token.volume_24h,
            creationTimestamp: token.created_timestamp,
            isComplete: token.complete,
            isLive: token.is_currently_live,
            bondingCurveAddress: token.bonding_curve,
            associatedBondingCurve: token.associated_bonding_curve,
            raydiumPoolAddress: token.raydium_pool,
            isNsfw: token.nsfw || false,
            hasSocial: !!(token.website || token.twitter || token.telegram)
        };
    }
    static normalizeAdvancedV2Token(token) {
        return {
            mint: token.mint || token.coinMint || token.address || '',
            name: token.name || token.token_name || '',
            symbol: token.symbol || token.ticker || token.token_symbol || '',
            creator: token.creator || token.dev || token.developer || '',
            description: token.description || '',
            imageUrl: token.image_uri || token.imageUrl || token.image,
            videoUrl: token.video_uri || token.videoUrl,
            bannerUrl: token.banner_uri || token.bannerUrl,
            metadataUrl: token.metadata_uri || token.metadataUrl,
            websiteUrl: token.website || token.website_url,
            twitterUrl: token.twitter || token.twitter_url,
            telegramUrl: token.telegram || token.telegram_url,
            marketCapUsd: token.usd_market_cap || token.marketCapUsd,
            marketCapSol: token.market_cap || token.marketCap,
            volume24h: token.volume_24h || token.volume,
            totalSupply: token.total_supply || token.totalSupply,
            creationTimestamp: token.created_timestamp || token.creationTime,
            graduationTimestamp: token.graduation_timestamp || token.graduationDate,
            isComplete: token.complete,
            isLive: token.is_currently_live,
            bondingCurveAddress: token.bonding_curve || token.bondingCurveAddress,
            associatedBondingCurve: token.associated_bonding_curve || token.associatedBondingCurve,
            raydiumPoolAddress: token.raydium_pool || token.poolAddress,
            virtualSolReserves: token.virtual_sol_reserves || token.virtualSolReserves,
            virtualTokenReserves: token.virtual_token_reserves || token.virtualTokenReserves,
            replyCount: token.reply_count || token.replyCount,
            lastReplyTimestamp: token.last_reply || token.lastReplyTimestamp,
            isNsfw: token.nsfw || false,
            hasSocial: !!(token.website || token.twitter || token.telegram)
        };
    }
    static normalizeLegacyToken(token) {
        const coinData = token.coin || token;
        const holders = token.holders?.map((holder) => ({
            holderId: holder.holderId,
            totalTokenAmountHeld: holder.totalTokenAmountHeld,
            ownedPercentage: holder.ownedPercentage,
            isSniper: holder.isSniper
        })) || [];
        return {
            mint: coinData.mint || coinData.coinMint || token.coinMint || '',
            name: coinData.name || token.name || '',
            symbol: coinData.symbol || coinData.ticker || token.ticker || '',
            creator: coinData.creator || coinData.dev || token.dev || '',
            description: coinData.description || token.description || '',
            curatedDescription: token.curatedDescription || token.description,
            imageUrl: coinData.image_uri || coinData.imageUrl || token.imageUrl,
            videoUrl: coinData.video_uri || coinData.videoUrl || token.videoUrl,
            bannerUrl: coinData.banner_uri || coinData.bannerUrl || token.bannerUrl,
            metadataUrl: coinData.metadata_uri || coinData.metadataUrl || token.metadataUrl,
            websiteUrl: coinData.website || token.website,
            twitterUrl: coinData.twitter || token.twitter,
            telegramUrl: coinData.telegram || token.telegram,
            marketCapSol: token.marketCap || coinData.market_cap,
            allTimeHighMarketCapSol: token.allTimeHighMarketCap,
            volume24h: token.volume || coinData.volume_24h,
            totalSupply: token.totalSupply || coinData.total_supply,
            currentMarketPrice: token.currentMarketPrice,
            numHolders: token.holders?.length,
            topHoldersPercentage: token.topHoldersPercentage,
            devHoldingsPercentage: token.devHoldingsPercentage,
            buyTransactions: token.buyTransactions,
            sellTransactions: token.sellTransactions,
            totalTransactions: token.transactions,
            holders: holders,
            sniperCount: token.sniperCount,
            sniperOwnedPercentage: token.sniperOwnedPercentage,
            creationTimestamp: token.creationTime || coinData.created_timestamp,
            graduationTimestamp: token.graduationDate || coinData.graduation_timestamp,
            bondingCurveProgress: token.bondingCurveProgress,
            isComplete: token.complete || coinData.complete,
            raydiumPoolAddress: token.poolAddress || coinData.raydium_pool,
            virtualSolReserves: token.virtualSolReserves || coinData.virtual_sol_reserves,
            virtualTokenReserves: token.virtualTokenReserves || coinData.virtual_token_reserves,
            replyCount: token.replyCount || coinData.reply_count,
            lastReplyTimestamp: token.lastReplyTimestamp || coinData.last_reply,
            hasSocial: !!(coinData.website || token.website || coinData.twitter || token.twitter || coinData.telegram || token.telegram),
            isNsfw: token.nsfw || coinData.nsfw || false,
            twitterReuseCount: token.twitterReuseCount
        };
    }
    static validateToken(token) {
        if (!token.mint) {
            logger_1.logger.warn('Token validation failed: missing mint address', {
                mint: token.mint,
                name: token.name,
                symbol: token.symbol,
                hasName: !!token.name,
                hasSymbol: !!token.symbol
            });
            return false;
        }
        if (!token.name && token.symbol) {
            token.name = token.symbol;
            logger_1.logger.debug('Using symbol as name for token', { mint: token.mint, symbol: token.symbol });
        }
        if (!token.symbol && token.name) {
            token.symbol = token.name;
            logger_1.logger.debug('Using name as symbol for token', { mint: token.mint, name: token.name });
        }
        if (!token.mint || (!token.name && !token.symbol)) {
            logger_1.logger.warn('Token validation failed: missing critical fields', {
                mint: token.mint,
                name: token.name,
                symbol: token.symbol
            });
            return false;
        }
        return true;
    }
}
exports.TokenNormalizer = TokenNormalizer;
exports.normalizeToken = TokenNormalizer.normalizeToken.bind(TokenNormalizer);
exports.validateToken = TokenNormalizer.validateToken.bind(TokenNormalizer);
//# sourceMappingURL=normalizer.js.map