"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.validateToken = exports.normalizeToken = exports.TokenNormalizer = void 0;
var api_1 = require("../types/api");
var logger_1 = require("./logger");
/**
 * Normalizes different API response formats into the unified token format
 */
var TokenNormalizer = /** @class */ (function () {
    function TokenNormalizer() {
    }
    /**
     * Main normalization function that routes to specific normalizers based on API type
     */
    TokenNormalizer.normalizeToken = function (rawToken, apiType) {
        try {
            switch (apiType) {
                case api_1.ApiType.FRONTEND_V3:
                    return this.normalizeFrontendV3Token(rawToken);
                case api_1.ApiType.ADVANCED_V2:
                    return this.normalizeAdvancedV2Token(rawToken);
                case api_1.ApiType.LEGACY:
                    return this.normalizeLegacyToken(rawToken);
                default:
                    throw new Error("Unknown API type: ".concat(apiType));
            }
        }
        catch (error) {
            logger_1.logger.error('Token normalization failed:', { apiType: apiType, error: error instanceof Error ? error.message : error, rawToken: rawToken });
            throw error;
        }
    };
    /**
     * Normalize Frontend API v3 token (For You)
     */
    TokenNormalizer.normalizeFrontendV3Token = function (token) {
        return {
            // Core Identifiers
            mint: token.mint,
            name: token.name || '',
            symbol: token.symbol || '',
            creator: token.creator || '',
            // Core Metadata
            description: token.description || '',
            imageUrl: token.image_uri,
            metadataUrl: token.metadata_uri,
            websiteUrl: token.website,
            twitterUrl: token.twitter,
            telegramUrl: token.telegram,
            // Market & Financial Data
            marketCapUsd: token.usd_market_cap,
            marketCapSol: token.market_cap,
            volume24h: token.volume_24h,
            // Pump.fun Lifecycle Status
            creationTimestamp: token.created_timestamp,
            isComplete: token.complete,
            isLive: token.is_currently_live,
            // Pump.fun Specific IDs & Data
            bondingCurveAddress: token.bonding_curve,
            associatedBondingCurve: token.associated_bonding_curve,
            raydiumPoolAddress: token.raydium_pool,
            // Social & Status Flags
            isNsfw: token.nsfw || false,
            hasSocial: !!(token.website || token.twitter || token.telegram)
        };
    };
    /**
     * Normalize Advanced API v2 token (Featured, New, Graduated)
     */
    TokenNormalizer.normalizeAdvancedV2Token = function (token) {
        return {
            // Core Identifiers
            mint: token.mint,
            name: token.name || '',
            symbol: token.symbol || '',
            creator: token.creator || '',
            // Core Metadata
            description: token.description || '',
            imageUrl: token.image_uri,
            metadataUrl: token.metadata_uri,
            websiteUrl: token.website,
            twitterUrl: token.twitter,
            telegramUrl: token.telegram,
            // Market & Financial Data
            marketCapUsd: token.usd_market_cap,
            marketCapSol: token.market_cap,
            volume24h: token.volume_24h,
            totalSupply: token.total_supply,
            // Pump.fun Lifecycle Status
            creationTimestamp: token.created_timestamp,
            graduationTimestamp: token.graduation_timestamp,
            isComplete: token.complete,
            isLive: token.is_currently_live,
            // Pump.fun Specific IDs & Data
            bondingCurveAddress: token.bonding_curve,
            associatedBondingCurve: token.associated_bonding_curve,
            raydiumPoolAddress: token.raydium_pool,
            virtualSolReserves: token.virtual_sol_reserves,
            virtualTokenReserves: token.virtual_token_reserves,
            // Social & Status Flags
            replyCount: token.reply_count,
            lastReplyTimestamp: token.last_reply,
            isNsfw: token.nsfw || false,
            hasSocial: !!(token.website || token.twitter || token.telegram)
        };
    };
    /**
     * Normalize Legacy API token (Runners)
     */
    TokenNormalizer.normalizeLegacyToken = function (token) {
        var _a, _b;
        var holders = ((_a = token.holders) === null || _a === void 0 ? void 0 : _a.map(function (holder) { return ({
            holderId: holder.holderId,
            totalTokenAmountHeld: holder.totalTokenAmountHeld,
            ownedPercentage: holder.ownedPercentage,
            isSniper: holder.isSniper
        }); })) || [];
        return {
            // Core Identifiers
            mint: token.coinMint,
            name: token.name || '',
            symbol: token.ticker || '',
            creator: token.dev || '',
            // Core Metadata
            description: token.description || '',
            curatedDescription: token.curatedDescription,
            imageUrl: token.imageUrl,
            videoUrl: token.videoUrl,
            bannerUrl: token.bannerUrl,
            metadataUrl: token.metadataUrl,
            websiteUrl: token.website,
            twitterUrl: token.twitter,
            telegramUrl: token.telegram,
            // Market & Financial Data
            marketCapSol: token.marketCap,
            allTimeHighMarketCapSol: token.allTimeHighMarketCap,
            volume24h: token.volume,
            totalSupply: token.totalSupply,
            currentMarketPrice: token.currentMarketPrice,
            // Transaction & Holder Analytics
            numHolders: (_b = token.holders) === null || _b === void 0 ? void 0 : _b.length,
            topHoldersPercentage: token.topHoldersPercentage,
            devHoldingsPercentage: token.devHoldingsPercentage,
            buyTransactions: token.buyTransactions,
            sellTransactions: token.sellTransactions,
            totalTransactions: token.transactions,
            holders: holders,
            // Sniper Analytics
            sniperCount: token.sniperCount,
            sniperOwnedPercentage: token.sniperOwnedPercentage,
            // Pump.fun Lifecycle Status
            creationTimestamp: token.creationTime,
            graduationTimestamp: token.graduationDate,
            bondingCurveProgress: token.bondingCurveProgress,
            isComplete: token.complete,
            // Pump.fun Specific IDs & Data
            raydiumPoolAddress: token.poolAddress,
            virtualSolReserves: token.virtualSolReserves,
            virtualTokenReserves: token.virtualTokenReserves,
            // Social & Status Flags
            replyCount: token.replyCount,
            lastReplyTimestamp: token.lastReplyTimestamp,
            hasSocial: !!(token.website || token.twitter || token.telegram),
            isNsfw: token.nsfw || false,
            twitterReuseCount: token.twitterReuseCount
        };
    };
    /**
     * Validate that a normalized token has required fields
     */
    TokenNormalizer.validateToken = function (token) {
        if (!token.mint || !token.name || !token.symbol) {
            logger_1.logger.warn('Token validation failed: missing required fields', {
                mint: token.mint,
                name: token.name,
                symbol: token.symbol
            });
            return false;
        }
        return true;
    };
    return TokenNormalizer;
}());
exports.TokenNormalizer = TokenNormalizer;
exports.normalizeToken = TokenNormalizer.normalizeToken.bind(TokenNormalizer);
exports.validateToken = TokenNormalizer.validateToken.bind(TokenNormalizer);
