"use strict";
var __spreadArray = (this && this.__spreadArray) || function (to, from, pack) {
    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {
        if (ar || !(i in from)) {
            if (!ar) ar = Array.prototype.slice.call(from, 0, i);
            ar[i] = from[i];
        }
    }
    return to.concat(ar || Array.prototype.slice.call(from));
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.logger = exports.Logger = void 0;
/**
 * Simple logging utility with different log levels
 */
var Logger = /** @class */ (function () {
    function Logger() {
        this.logLevel = process.env.LOG_LEVEL || 'info';
    }
    Logger.prototype.shouldLog = function (level) {
        var levels = ['error', 'warn', 'info', 'debug'];
        var currentLevelIndex = levels.indexOf(this.logLevel);
        var messageLevelIndex = levels.indexOf(level);
        return messageLevelIndex <= currentLevelIndex;
    };
    Logger.prototype.formatMessage = function (level, message) {
        var args = [];
        for (var _i = 2; _i < arguments.length; _i++) {
            args[_i - 2] = arguments[_i];
        }
        var timestamp = new Date().toISOString();
        var formattedArgs = args.length > 0 ? ' ' + args.map(function (arg) {
            return typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg);
        }).join(' ') : '';
        return "[".concat(timestamp, "] [").concat(level.toUpperCase(), "] ").concat(message).concat(formattedArgs);
    };
    Logger.prototype.error = function (message) {
        var args = [];
        for (var _i = 1; _i < arguments.length; _i++) {
            args[_i - 1] = arguments[_i];
        }
        if (this.shouldLog('error')) {
            console.error(this.formatMessage.apply(this, __spreadArray(['error', message], args, false)));
        }
    };
    Logger.prototype.warn = function (message) {
        var args = [];
        for (var _i = 1; _i < arguments.length; _i++) {
            args[_i - 1] = arguments[_i];
        }
        if (this.shouldLog('warn')) {
            console.warn(this.formatMessage.apply(this, __spreadArray(['warn', message], args, false)));
        }
    };
    Logger.prototype.info = function (message) {
        var args = [];
        for (var _i = 1; _i < arguments.length; _i++) {
            args[_i - 1] = arguments[_i];
        }
        if (this.shouldLog('info')) {
            console.log(this.formatMessage.apply(this, __spreadArray(['info', message], args, false)));
        }
    };
    Logger.prototype.debug = function (message) {
        var args = [];
        for (var _i = 1; _i < arguments.length; _i++) {
            args[_i - 1] = arguments[_i];
        }
        if (this.shouldLog('debug')) {
            console.log(this.formatMessage.apply(this, __spreadArray(['debug', message], args, false)));
        }
    };
    /**
     * Log API polling activity
     */
    Logger.prototype.polling = function (category, action, details) {
        this.info("[POLLING] ".concat(category, " - ").concat(action), details);
    };
    /**
     * Log database operations
     */
    Logger.prototype.database = function (operation, table, details) {
        this.info("[DATABASE] ".concat(operation, " on ").concat(table), details);
    };
    /**
     * Log API requests
     */
    Logger.prototype.api = function (method, url, status, details) {
        this.info("[API] ".concat(method, " ").concat(url, " ").concat(status ? "(".concat(status, ")") : ''), details);
    };
    return Logger;
}());
exports.Logger = Logger;
exports.logger = new Logger();
