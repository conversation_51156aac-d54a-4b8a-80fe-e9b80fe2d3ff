"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.apiService = exports.ApiService = void 0;
const axios_1 = __importDefault(require("axios"));
const constants_1 = require("../config/constants");
const logger_1 = require("../utils/logger");
class ApiService {
    constructor() {
        this.useProxy = process.env.USE_PROXY === 'true';
        this.proxyBaseUrl = process.env.PROXY_BASE_URL || 'http://localhost:3003/api/proxy';
    }
    async makeRequestWithRetry(url, params = {}, retryCount = 0) {
        try {
            let requestUrl = url;
            let requestConfig = {
                timeout: 30000,
                headers: {
                    'Accept': 'application/json',
                    'Cache-Control': 'no-cache'
                }
            };
            if (this.useProxy && !url.startsWith('http://localhost')) {
                const urlObj = new URL(url);
                const proxyUrl = new URL(this.proxyBaseUrl);
                proxyUrl.searchParams.set('url', urlObj.origin + urlObj.pathname);
                Object.entries(params).forEach(([key, value]) => {
                    proxyUrl.searchParams.set(key, String(value));
                });
                requestUrl = proxyUrl.toString();
                requestConfig.params = {};
            }
            else {
                requestConfig.params = params;
            }
            logger_1.logger.api('GET', requestUrl);
            logger_1.logger.debug('Request config:', { url: requestUrl, params: requestConfig.params });
            const response = await axios_1.default.get(requestUrl, requestConfig);
            let data = response.data;
            if (data && typeof data === 'object' && !Array.isArray(data) && data.coins) {
                logger_1.logger.debug('Unwrapping data.coins structure');
                data = data.coins;
            }
            logger_1.logger.api('GET', requestUrl, response.status, {
                dataLength: Array.isArray(data) ? data.length : 'N/A',
                dataType: typeof data,
                isWrapped: response.data !== data,
                sampleData: Array.isArray(data) && data.length > 0 ?
                    { firstItem: Object.keys(data[0] || {}) } : 'N/A'
            });
            return data;
        }
        catch (error) {
            const errorMessage = error.response?.data?.message || error.message || 'Unknown error';
            const statusCode = error.response?.status;
            logger_1.logger.error(`API request failed (attempt ${retryCount + 1}/${constants_1.RETRY_CONFIG.MAX_RETRIES + 1}):`, {
                url,
                status: statusCode,
                error: errorMessage
            });
            if (retryCount < constants_1.RETRY_CONFIG.MAX_RETRIES) {
                const delay = constants_1.RETRY_CONFIG.RETRY_DELAY * Math.pow(constants_1.RETRY_CONFIG.BACKOFF_MULTIPLIER, retryCount);
                logger_1.logger.info(`Retrying in ${delay}ms...`);
                await new Promise(resolve => setTimeout(resolve, delay));
                return this.makeRequestWithRetry(url, params, retryCount + 1);
            }
            throw new Error(`API request failed after ${constants_1.RETRY_CONFIG.MAX_RETRIES + 1} attempts: ${errorMessage}`);
        }
    }
    async fetchForYouTokens() {
        const url = `${constants_1.API_URLS.FRONTEND_V3}/coins/for-you`;
        const params = {
            offset: constants_1.API_PARAMS.OFFSET,
            limit: constants_1.API_PARAMS.LIMIT,
            includeNsfw: constants_1.API_PARAMS.INCLUDE_NSFW
        };
        return this.makeRequestWithRetry(url, params);
    }
    async fetchFeaturedTokens() {
        const url = `${constants_1.API_URLS.ADVANCED_V2}/coins/list`;
        const params = {
            sortBy: 'featured',
            limit: constants_1.API_PARAMS.LIMIT,
            offset: constants_1.API_PARAMS.OFFSET
        };
        return this.makeRequestWithRetry(url, params);
    }
    async fetchNewTokens() {
        const url = `${constants_1.API_URLS.ADVANCED_V2}/coins/list`;
        const params = {
            sortBy: 'new',
            limit: constants_1.API_PARAMS.LIMIT,
            offset: constants_1.API_PARAMS.OFFSET
        };
        return this.makeRequestWithRetry(url, params);
    }
    async fetchGraduatedTokens() {
        const url = `${constants_1.API_URLS.ADVANCED_V2}/coins/graduated`;
        const params = {
            limit: constants_1.API_PARAMS.LIMIT,
            offset: constants_1.API_PARAMS.OFFSET
        };
        return this.makeRequestWithRetry(url, params);
    }
    async fetchRunnersTokens() {
        const url = `${constants_1.API_URLS.LEGACY}/runners`;
        const params = {
            limit: constants_1.API_PARAMS.LIMIT,
            offset: constants_1.API_PARAMS.OFFSET
        };
        return this.makeRequestWithRetry(url, params);
    }
}
exports.ApiService = ApiService;
exports.apiService = new ApiService();
//# sourceMappingURL=apiService.js.map