-- ================================================================= 
--  Fix Database Policies for Data Ingestion Server
--  Run this if you're getting RLS policy violations
-- ================================================================= 

-- Drop existing restrictive policies if they exist
DROP POLICY IF EXISTS "Allow public read access" ON public.tokens;
DROP POLICY IF EXISTS "Allow public read access" ON public.categories;
DROP POLICY IF EXISTS "Allow public read access" ON public.token_categories;
DROP POLICY IF EXISTS "Allow public read access" ON public.market_data_history;
DROP POLICY IF EXISTS "Allow public read access" ON public.milestones;
DROP POLICY IF EXISTS "Allow public read access" ON public.achieved_milestones;

-- Create policies to allow anon key to write data (for server operations)
CREATE POLICY "Allow anon write access" ON public.tokens FOR ALL USING (true);
CREATE POLICY "Allow anon write access" ON public.categories FOR ALL USING (true);
CREATE POLICY "Allow anon write access" ON public.token_categories FOR ALL USING (true);
CREATE POLICY "Allow anon write access" ON public.market_data_history FOR ALL USING (true);
CREATE POLICY "Allow anon write access" ON public.milestones FOR ALL USING (true);
CREATE POLICY "Allow anon write access" ON public.achieved_milestones FOR ALL USING (true);

-- Create policies to allow service role full access
CREATE POLICY "Allow service role full access" ON public.tokens FOR ALL USING (auth.role() = 'service_role');
CREATE POLICY "Allow service role full access" ON public.categories FOR ALL USING (auth.role() = 'service_role');
CREATE POLICY "Allow service role full access" ON public.token_categories FOR ALL USING (auth.role() = 'service_role');
CREATE POLICY "Allow service role full access" ON public.market_data_history FOR ALL USING (auth.role() = 'service_role');
CREATE POLICY "Allow service role full access" ON public.milestones FOR ALL USING (auth.role() = 'service_role');
CREATE POLICY "Allow service role full access" ON public.achieved_milestones FOR ALL USING (auth.role() = 'service_role');

-- Verify policies are created
SELECT schemaname, tablename, policyname, permissive, roles, cmd, qual 
FROM pg_policies 
WHERE schemaname = 'public' 
ORDER BY tablename, policyname;
