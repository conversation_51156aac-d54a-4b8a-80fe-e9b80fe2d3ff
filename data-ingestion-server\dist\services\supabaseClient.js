"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getSupabaseService = getSupabaseService;
exports.getSupabase = getSupabase;
const supabase_js_1 = require("@supabase/supabase-js");
const logger_1 = require("../utils/logger");
class SupabaseService {
    constructor() {
        const supabaseUrl = process.env.SUPABASE_URL;
        const supabaseKey = process.env.SUPABASE_ANON_KEY;
        if (!supabaseUrl || !supabaseKey) {
            throw new Error('Missing required Supabase environment variables: SUPABASE_URL and SUPABASE_ANON_KEY');
        }
        this.client = (0, supabase_js_1.createClient)(supabaseUrl, supabaseKey, {
            auth: {
                persistSession: false
            }
        });
        logger_1.logger.info('Supabase client initialized successfully');
    }
    static getInstance() {
        if (!SupabaseService.instance) {
            SupabaseService.instance = new SupabaseService();
        }
        return SupabaseService.instance;
    }
    getClient() {
        return this.client;
    }
    async testConnection() {
        try {
            const { data, error } = await this.client
                .from('categories')
                .select('count')
                .limit(1);
            if (error) {
                logger_1.logger.error('Database connection test failed:', error);
                return false;
            }
            logger_1.logger.info('Database connection test successful');
            return true;
        }
        catch (error) {
            logger_1.logger.error('Database connection test error:', error);
            return false;
        }
    }
}
function getSupabaseService() {
    return SupabaseService.getInstance();
}
function getSupabase() {
    return SupabaseService.getInstance().getClient();
}
//# sourceMappingURL=supabaseClient.js.map