import dotenv from 'dotenv';
import { getSupabaseService } from './services/supabaseClient';
import { pollingService } from './services/pollingService';
import { healthService } from './services/healthService';
import { logger } from './utils/logger';

// Load environment variables
dotenv.config();

/**
 * Main application class
 */
class DataIngestionServer {
  private isShuttingDown = false;

  /**
   * Initialize and start the server
   */
  async start(): Promise<void> {
    try {
      logger.info('🚀 Starting Pump.fun Data Ingestion Server...');

      // Validate environment variables
      this.validateEnvironment();

      // Test database connection
      const dbConnected = await getSupabaseService().testConnection();
      if (!dbConnected) {
        throw new Error('Failed to connect to database');
      }

      // Setup graceful shutdown handlers
      this.setupShutdownHandlers();

      // Start health service
      const healthPort = parseInt(process.env.HEALTH_PORT || '3005');
      healthService.start(healthPort);

      // Start polling service
      pollingService.start();

      logger.info('✅ Data Ingestion Server started successfully');
      logger.info('📊 Polling jobs are now running...');
      
      // Log polling status
      const status = pollingService.getStatus();
      Object.entries(status).forEach(([category, running]) => {
        logger.info(`   ${category}: ${running ? '✅ Running' : '❌ Stopped'}`);
      });

    } catch (error) {
      logger.error('❌ Failed to start server:', error);
      process.exit(1);
    }
  }

  /**
   * Validate required environment variables
   */
  private validateEnvironment(): void {
    const required = ['SUPABASE_URL', 'SUPABASE_ANON_KEY'];
    const missing = required.filter(key => !process.env[key]);

    if (missing.length > 0) {
      throw new Error(`Missing required environment variables: ${missing.join(', ')}`);
    }

    logger.info('✅ Environment variables validated');
  }

  /**
   * Setup graceful shutdown handlers
   */
  private setupShutdownHandlers(): void {
    const shutdown = async (signal: string) => {
      if (this.isShuttingDown) {
        logger.warn('Force shutdown initiated');
        process.exit(1);
      }

      this.isShuttingDown = true;
      logger.info(`🛑 Received ${signal}, shutting down gracefully...`);

      try {
        // Stop services
        pollingService.stop();
        healthService.stop();

        logger.info('✅ Server shutdown completed');
        process.exit(0);
      } catch (error) {
        logger.error('❌ Error during shutdown:', error);
        process.exit(1);
      }
    };

    // Handle different shutdown signals
    process.on('SIGTERM', () => shutdown('SIGTERM'));
    process.on('SIGINT', () => shutdown('SIGINT'));
    process.on('SIGUSR2', () => shutdown('SIGUSR2')); // nodemon restart

    // Handle uncaught exceptions
    process.on('uncaughtException', (error) => {
      logger.error('💥 Uncaught Exception:', error);
      shutdown('uncaughtException');
    });

    process.on('unhandledRejection', (reason, promise) => {
      logger.error('💥 Unhandled Rejection at:', promise, 'reason:', reason);
      shutdown('unhandledRejection');
    });
  }
}

// Start the server
const server = new DataIngestionServer();
server.start().catch((error) => {
  logger.error('💥 Fatal error starting server:', error);
  process.exit(1);
});
