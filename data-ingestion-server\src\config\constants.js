"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.LOG_LEVELS = exports.RETRY_CONFIG = exports.DB_TABLES = exports.MILESTONE_MULTIPLIERS = exports.API_PARAMS = exports.CATEGORY_CONFIGS = exports.API_URLS = void 0;
var api_1 = require("../types/api");
// API Base URLs
exports.API_URLS = {
    FRONTEND_V3: 'https://frontend-api-v3.pump.fun',
    ADVANCED_V2: 'https://advanced-api-v2.pump.fun',
    LEGACY: 'https://pump.fun/api'
};
// Category configurations with their respective endpoints and polling intervals
exports.CATEGORY_CONFIGS = [
    {
        slug: 'for-you',
        name: 'For You',
        apiType: api_1.ApiType.FRONTEND_V3,
        endpoint: '/coins/for-you',
        pollInterval: parseInt(process.env.POLL_INTERVAL_FOR_YOU || '5')
    },
    {
        slug: 'featured',
        name: 'Featured',
        apiType: api_1.ApiType.ADVANCED_V2,
        endpoint: '/coins',
        pollInterval: parseInt(process.env.POLL_INTERVAL_FEATURED || '5')
    },
    {
        slug: 'new',
        name: 'New',
        apiType: api_1.ApiType.ADVANCED_V2,
        endpoint: '/coins',
        pollInterval: parseInt(process.env.POLL_INTERVAL_NEW || '2')
    },
    {
        slug: 'graduated',
        name: 'Graduated',
        apiType: api_1.ApiType.ADVANCED_V2,
        endpoint: '/coins/graduated',
        pollInterval: parseInt(process.env.POLL_INTERVAL_GRADUATED || '5')
    },
    {
        slug: 'runners',
        name: 'Runners',
        apiType: api_1.ApiType.LEGACY,
        endpoint: '/runners',
        pollInterval: parseInt(process.env.POLL_INTERVAL_RUNNERS || '5')
    }
];
// API request parameters
exports.API_PARAMS = {
    LIMIT: parseInt(process.env.API_LIMIT || '50'),
    OFFSET: parseInt(process.env.API_OFFSET || '0'),
    INCLUDE_NSFW: process.env.INCLUDE_NSFW === 'true'
};
// Milestone multipliers for tracking token performance
exports.MILESTONE_MULTIPLIERS = [2, 3, 5, 10, 20, 50, 100, 200, 300, 500, 1000];
// Database table names
exports.DB_TABLES = {
    TOKENS: 'tokens',
    CATEGORIES: 'categories',
    TOKEN_CATEGORIES: 'token_categories',
    MARKET_DATA_HISTORY: 'market_data_history',
    MILESTONES: 'milestones',
    ACHIEVED_MILESTONES: 'achieved_milestones'
};
// Retry configuration for API calls
exports.RETRY_CONFIG = {
    MAX_RETRIES: 3,
    RETRY_DELAY: 1000, // milliseconds
    BACKOFF_MULTIPLIER: 2
};
// Logging levels
exports.LOG_LEVELS = {
    ERROR: 'error',
    WARN: 'warn',
    INFO: 'info',
    DEBUG: 'debug'
};
