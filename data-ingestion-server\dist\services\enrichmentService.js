"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.enrichmentService = exports.EnrichmentService = void 0;
const axios_1 = __importDefault(require("axios"));
const constants_1 = require("../config/constants");
const logger_1 = require("../utils/logger");
class EnrichmentService {
    constructor() {
        this.baseUrl = 'https://api.dexscreener.com/latest/dex';
    }
    async enrichToken(mint) {
        try {
            logger_1.logger.debug(`Enriching token: ${mint}`);
            const url = `${this.baseUrl}/tokens/${mint}`;
            const response = await this.makeRequestWithRetry(url);
            if (!response?.data?.pairs || response.data.pairs.length === 0) {
                logger_1.logger.debug(`No DexScreener data found for token: ${mint}`);
                return null;
            }
            const pairs = response.data.pairs;
            const pumpfunPair = pairs.find((pair) => pair.dexId === 'pumpfun');
            const bestPair = pumpfunPair || pairs.sort((a, b) => (b.volume?.h24 || 0) - (a.volume?.h24 || 0))[0];
            if (!bestPair) {
                logger_1.logger.debug(`No suitable pair found for token: ${mint}`);
                return null;
            }
            const enrichmentData = {
                dexscreenerUrl: bestPair.url || '',
                fdv: parseFloat(bestPair.fdv) || 0,
                priceNative: parseFloat(bestPair.priceNative) || 0,
                priceUsd: parseFloat(bestPair.priceUsd) || 0,
                priceChangeM5: bestPair.priceChange?.m5 || 0,
                priceChangeH1: bestPair.priceChange?.h1 || 0,
                priceChangeH6: bestPair.priceChange?.h6 || 0,
                priceChangeH24: bestPair.priceChange?.h24 || 0,
                graduationTimestamp: bestPair.pairCreatedAt || 0,
                txnsM5Buys: bestPair.txns?.m5?.buys || 0,
                txnsM5Sells: bestPair.txns?.m5?.sells || 0,
                txnsH1Buys: bestPair.txns?.h1?.buys || 0,
                txnsH1Sells: bestPair.txns?.h1?.sells || 0,
                txnsH6Buys: bestPair.txns?.h6?.buys || 0,
                txnsH6Sells: bestPair.txns?.h6?.sells || 0,
                txnsH24Buys: bestPair.txns?.h24?.buys || 0,
                txnsH24Sells: bestPair.txns?.h24?.sells || 0,
            };
            logger_1.logger.debug(`Successfully enriched token: ${mint}`, {
                dexscreenerUrl: enrichmentData.dexscreenerUrl,
                fdv: enrichmentData.fdv,
                priceUsd: enrichmentData.priceUsd
            });
            return enrichmentData;
        }
        catch (error) {
            if (axios_1.default.isAxiosError(error)) {
                if (error.response?.status === 404) {
                    logger_1.logger.debug(`Token not found on DexScreener: ${mint}`);
                    return null;
                }
                if (error.response?.status && error.response.status >= 500) {
                    logger_1.logger.warn(`DexScreener server error for token ${mint}:`, error.response.status);
                    return null;
                }
            }
            logger_1.logger.error(`Failed to enrich token ${mint}:`, error);
            return null;
        }
    }
    async enrichTokens(mints) {
        const enrichmentMap = new Map();
        logger_1.logger.debug(`Enriching ${mints.length} tokens`);
        const batchSize = 5;
        for (let i = 0; i < mints.length; i += batchSize) {
            const batch = mints.slice(i, i + batchSize);
            const promises = batch.map(async (mint) => {
                const enrichmentData = await this.enrichToken(mint);
                if (enrichmentData) {
                    enrichmentMap.set(mint, enrichmentData);
                }
            });
            await Promise.all(promises);
            if (i + batchSize < mints.length) {
                await new Promise(resolve => setTimeout(resolve, 100));
            }
        }
        logger_1.logger.debug(`Successfully enriched ${enrichmentMap.size} out of ${mints.length} tokens`);
        return enrichmentMap;
    }
    async makeRequestWithRetry(url) {
        let lastError;
        for (let attempt = 0; attempt <= constants_1.RETRY_CONFIG.MAX_RETRIES; attempt++) {
            try {
                const response = await axios_1.default.get(url, {
                    timeout: 10000,
                    headers: {
                        'User-Agent': 'pump-fun-data-ingestion/1.0'
                    }
                });
                return response;
            }
            catch (error) {
                lastError = error;
                if (axios_1.default.isAxiosError(error)) {
                    if (error.response?.status && error.response.status >= 400 && error.response.status < 500 && error.response.status !== 429) {
                        throw error;
                    }
                }
                if (attempt < constants_1.RETRY_CONFIG.MAX_RETRIES) {
                    const delay = constants_1.RETRY_CONFIG.RETRY_DELAY * Math.pow(constants_1.RETRY_CONFIG.BACKOFF_MULTIPLIER, attempt);
                    logger_1.logger.debug(`Retrying DexScreener request in ${delay}ms (attempt ${attempt + 1}/${constants_1.RETRY_CONFIG.MAX_RETRIES + 1})`);
                    await new Promise(resolve => setTimeout(resolve, delay));
                }
            }
        }
        throw lastError;
    }
}
exports.EnrichmentService = EnrichmentService;
exports.enrichmentService = new EnrichmentService();
//# sourceMappingURL=enrichmentService.js.map