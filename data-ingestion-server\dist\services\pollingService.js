"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.pollingService = exports.PollingService = void 0;
const cron = __importStar(require("node-cron"));
const apiService_1 = require("./apiService");
const dataProcessor_1 = require("./dataProcessor");
const normalizer_1 = require("../utils/normalizer");
const constants_1 = require("../config/constants");
const logger_1 = require("../utils/logger");
class PollingService {
    constructor() {
        this.jobs = new Map();
        this.jobStatus = new Map();
    }
    start() {
        logger_1.logger.info('Starting polling service...');
        constants_1.CATEGORY_CONFIGS.forEach(config => {
            this.startPollingJob(config.slug, config.pollInterval, config.apiType);
        });
        logger_1.logger.info(`Started ${constants_1.CATEGORY_CONFIGS.length} polling jobs`);
    }
    stop() {
        logger_1.logger.info('Stopping polling service...');
        this.jobs.forEach((job, category) => {
            job.stop();
            logger_1.logger.info(`Stopped polling job for ${category}`);
        });
        this.jobs.clear();
        this.jobStatus.clear();
        logger_1.logger.info('All polling jobs stopped');
    }
    startPollingJob(categorySlug, intervalMinutes, apiType) {
        const cronExpression = `*/${intervalMinutes} * * * *`;
        const job = cron.schedule(cronExpression, async () => {
            await this.pollCategory(categorySlug, apiType);
        }, {
            scheduled: false
        });
        this.jobs.set(categorySlug, job);
        job.start();
        logger_1.logger.info(`Started polling job for ${categorySlug} (every ${intervalMinutes} minutes)`);
        setImmediate(() => {
            this.pollCategory(categorySlug, apiType);
        });
    }
    async pollCategory(categorySlug, apiType) {
        try {
            logger_1.logger.polling(categorySlug, 'Starting poll');
            const rawTokens = await this.fetchTokensByCategory(categorySlug);
            if (!rawTokens) {
                logger_1.logger.polling(categorySlug, 'API returned null/undefined response');
                return;
            }
            if (!Array.isArray(rawTokens)) {
                logger_1.logger.polling(categorySlug, 'API returned non-array response', { type: typeof rawTokens, data: rawTokens });
                return;
            }
            if (rawTokens.length === 0) {
                logger_1.logger.polling(categorySlug, 'No tokens returned from API');
                return;
            }
            logger_1.logger.polling(categorySlug, 'Fetched tokens', { count: rawTokens.length });
            const normalizedTokens = rawTokens
                .map(token => {
                try {
                    return (0, normalizer_1.normalizeToken)(token, apiType);
                }
                catch (error) {
                    logger_1.logger.error(`Failed to normalize token in ${categorySlug}:`, error);
                    return null;
                }
            })
                .filter((token) => token !== null)
                .filter(normalizer_1.validateToken);
            if (normalizedTokens.length === 0) {
                logger_1.logger.polling(categorySlug, 'No valid tokens after normalization');
                return;
            }
            logger_1.logger.polling(categorySlug, 'Normalized tokens', {
                count: normalizedTokens.length,
                filtered: rawTokens.length - normalizedTokens.length
            });
            await dataProcessor_1.dataProcessor.processAndUpsertData(normalizedTokens, categorySlug);
            logger_1.logger.polling(categorySlug, 'Poll completed successfully');
        }
        catch (error) {
            logger_1.logger.error(`Polling failed for ${categorySlug}:`, error);
        }
    }
    async fetchTokensByCategory(categorySlug) {
        switch (categorySlug) {
            case 'for-you':
                return apiService_1.apiService.fetchForYouTokens();
            case 'featured':
                return apiService_1.apiService.fetchFeaturedTokens();
            case 'new':
                return apiService_1.apiService.fetchNewTokens();
            case 'graduated':
                return apiService_1.apiService.fetchGraduatedTokens();
            case 'runners':
                return apiService_1.apiService.fetchRunnersTokens();
            default:
                throw new Error(`Unknown category: ${categorySlug}`);
        }
    }
    getStatus() {
        const status = {};
        this.jobs.forEach((job, category) => {
            status[category] = this.jobStatus.get(category) || false;
        });
        return status;
    }
    async triggerPoll(categorySlug) {
        const config = constants_1.CATEGORY_CONFIGS.find(c => c.slug === categorySlug);
        if (!config) {
            throw new Error(`Unknown category: ${categorySlug}`);
        }
        logger_1.logger.info(`Manually triggering poll for ${categorySlug}`);
        await this.pollCategory(categorySlug, config.apiType);
    }
}
exports.PollingService = PollingService;
exports.pollingService = new PollingService();
//# sourceMappingURL=pollingService.js.map