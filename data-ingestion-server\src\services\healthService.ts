import express from 'express';
import { getSupabaseService } from './supabaseClient';
import { pollingService } from './pollingService';
import { logger } from '../utils/logger';

/**
 * Simple health check service for monitoring
 */
export class HealthService {
  private app: express.Application;
  private server: any;

  constructor() {
    this.app = express();
    this.setupRoutes();
  }

  /**
   * Setup health check routes
   */
  private setupRoutes(): void {
    this.app.get('/health', async (req, res) => {
      try {
        const health = await this.getHealthStatus();
        const statusCode = health.status === 'healthy' ? 200 : 503;
        res.status(statusCode).json(health);
      } catch (error) {
        logger.error('Health check failed:', error);
        res.status(503).json({
          status: 'unhealthy',
          error: 'Health check failed'
        });
      }
    });

    this.app.get('/status', (req, res) => {
      const pollingStatus = pollingService.getStatus();
      res.json({
        status: 'running',
        polling: pollingStatus,
        uptime: process.uptime(),
        memory: process.memoryUsage(),
        timestamp: new Date().toISOString()
      });
    });
  }

  /**
   * Get comprehensive health status
   */
  private async getHealthStatus(): Promise<any> {
    const dbHealthy = await getSupabaseService().testConnection();
    const pollingStatus = pollingService.getStatus();
    const pollingHealthy = Object.values(pollingStatus).some(running => running);

    const status = dbHealthy && pollingHealthy ? 'healthy' : 'unhealthy';

    return {
      status,
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      database: dbHealthy ? 'connected' : 'disconnected',
      polling: {
        status: pollingHealthy ? 'running' : 'stopped',
        jobs: pollingStatus
      },
      memory: process.memoryUsage()
    };
  }

  /**
   * Start the health service
   */
  start(port: number = 3005): void {
    this.server = this.app.listen(port, () => {
      logger.info(`Health service running on port ${port}`);
      logger.info(`Health check: http://localhost:${port}/health`);
      logger.info(`Status endpoint: http://localhost:${port}/status`);
    });
  }

  /**
   * Stop the health service
   */
  stop(): void {
    if (this.server) {
      this.server.close();
      logger.info('Health service stopped');
    }
  }
}

export const healthService = new HealthService();
