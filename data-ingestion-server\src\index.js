"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === "function" ? Iterator : Object).prototype);
    return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2), typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var dotenv_1 = require("dotenv");
var supabaseClient_1 = require("./services/supabaseClient");
var pollingService_1 = require("./services/pollingService");
var healthService_1 = require("./services/healthService");
var logger_1 = require("./utils/logger");
// Load environment variables
dotenv_1.default.config();
/**
 * Main application class
 */
var DataIngestionServer = /** @class */ (function () {
    function DataIngestionServer() {
        this.isShuttingDown = false;
    }
    /**
     * Initialize and start the server
     */
    DataIngestionServer.prototype.start = function () {
        return __awaiter(this, void 0, void 0, function () {
            var dbConnected, healthPort, status_1, error_1;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        _a.trys.push([0, 2, , 3]);
                        logger_1.logger.info('🚀 Starting Pump.fun Data Ingestion Server...');
                        // Validate environment variables
                        this.validateEnvironment();
                        return [4 /*yield*/, supabaseClient_1.supabaseService.testConnection()];
                    case 1:
                        dbConnected = _a.sent();
                        if (!dbConnected) {
                            throw new Error('Failed to connect to database');
                        }
                        // Setup graceful shutdown handlers
                        this.setupShutdownHandlers();
                        healthPort = parseInt(process.env.HEALTH_PORT || '3005');
                        healthService_1.healthService.start(healthPort);
                        // Start polling service
                        pollingService_1.pollingService.start();
                        logger_1.logger.info('✅ Data Ingestion Server started successfully');
                        logger_1.logger.info('📊 Polling jobs are now running...');
                        status_1 = pollingService_1.pollingService.getStatus();
                        Object.entries(status_1).forEach(function (_a) {
                            var category = _a[0], running = _a[1];
                            logger_1.logger.info("   ".concat(category, ": ").concat(running ? '✅ Running' : '❌ Stopped'));
                        });
                        return [3 /*break*/, 3];
                    case 2:
                        error_1 = _a.sent();
                        logger_1.logger.error('❌ Failed to start server:', error_1);
                        process.exit(1);
                        return [3 /*break*/, 3];
                    case 3: return [2 /*return*/];
                }
            });
        });
    };
    /**
     * Validate required environment variables
     */
    DataIngestionServer.prototype.validateEnvironment = function () {
        var required = ['SUPABASE_URL', 'SUPABASE_ANON_KEY'];
        var missing = required.filter(function (key) { return !process.env[key]; });
        if (missing.length > 0) {
            throw new Error("Missing required environment variables: ".concat(missing.join(', ')));
        }
        logger_1.logger.info('✅ Environment variables validated');
    };
    /**
     * Setup graceful shutdown handlers
     */
    DataIngestionServer.prototype.setupShutdownHandlers = function () {
        var _this = this;
        var shutdown = function (signal) { return __awaiter(_this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                if (this.isShuttingDown) {
                    logger_1.logger.warn('Force shutdown initiated');
                    process.exit(1);
                }
                this.isShuttingDown = true;
                logger_1.logger.info("\uD83D\uDED1 Received ".concat(signal, ", shutting down gracefully..."));
                try {
                    // Stop services
                    pollingService_1.pollingService.stop();
                    healthService_1.healthService.stop();
                    logger_1.logger.info('✅ Server shutdown completed');
                    process.exit(0);
                }
                catch (error) {
                    logger_1.logger.error('❌ Error during shutdown:', error);
                    process.exit(1);
                }
                return [2 /*return*/];
            });
        }); };
        // Handle different shutdown signals
        process.on('SIGTERM', function () { return shutdown('SIGTERM'); });
        process.on('SIGINT', function () { return shutdown('SIGINT'); });
        process.on('SIGUSR2', function () { return shutdown('SIGUSR2'); }); // nodemon restart
        // Handle uncaught exceptions
        process.on('uncaughtException', function (error) {
            logger_1.logger.error('💥 Uncaught Exception:', error);
            shutdown('uncaughtException');
        });
        process.on('unhandledRejection', function (reason, promise) {
            logger_1.logger.error('💥 Unhandled Rejection at:', promise, 'reason:', reason);
            shutdown('unhandledRejection');
        });
    };
    return DataIngestionServer;
}());
// Start the server
var server = new DataIngestionServer();
server.start().catch(function (error) {
    logger_1.logger.error('💥 Fatal error starting server:', error);
    process.exit(1);
});
