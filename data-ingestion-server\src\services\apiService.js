"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === "function" ? Iterator : Object).prototype);
    return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2), typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.apiService = exports.ApiService = void 0;
var axios_1 = require("axios");
var constants_1 = require("../config/constants");
var logger_1 = require("../utils/logger");
/**
 * Service for making API requests to pump.fun endpoints
 */
var ApiService = /** @class */ (function () {
    function ApiService() {
        this.useProxy = process.env.USE_PROXY === 'true';
        this.proxyBaseUrl = process.env.PROXY_BASE_URL || 'http://localhost:3003/api/proxy';
    }
    /**
     * Make a request with retry logic
     */
    ApiService.prototype.makeRequestWithRetry = function (url_1) {
        return __awaiter(this, arguments, void 0, function (url, params, retryCount) {
            var requestUrl, requestConfig, urlObj, proxyUrl_1, response, error_1, errorMessage, statusCode, delay_1;
            var _a, _b, _c;
            if (params === void 0) { params = {}; }
            if (retryCount === void 0) { retryCount = 0; }
            return __generator(this, function (_d) {
                switch (_d.label) {
                    case 0:
                        _d.trys.push([0, 2, , 5]);
                        requestUrl = url;
                        requestConfig = {
                            timeout: 30000,
                            headers: {
                                'Accept': 'application/json',
                                'Cache-Control': 'no-cache'
                            }
                        };
                        // Use proxy if configured
                        if (this.useProxy && !url.startsWith('http://localhost')) {
                            urlObj = new URL(url);
                            proxyUrl_1 = new URL(this.proxyBaseUrl);
                            proxyUrl_1.searchParams.set('url', urlObj.origin + urlObj.pathname);
                            // Add original query parameters
                            Object.entries(params).forEach(function (_a) {
                                var key = _a[0], value = _a[1];
                                proxyUrl_1.searchParams.set(key, String(value));
                            });
                            requestUrl = proxyUrl_1.toString();
                            requestConfig.params = {};
                        }
                        else {
                            requestConfig.params = params;
                        }
                        logger_1.logger.api('GET', requestUrl);
                        return [4 /*yield*/, axios_1.default.get(requestUrl, requestConfig)];
                    case 1:
                        response = _d.sent();
                        logger_1.logger.api('GET', requestUrl, response.status, { dataLength: Array.isArray(response.data) ? response.data.length : 'N/A' });
                        return [2 /*return*/, response.data];
                    case 2:
                        error_1 = _d.sent();
                        errorMessage = ((_b = (_a = error_1.response) === null || _a === void 0 ? void 0 : _a.data) === null || _b === void 0 ? void 0 : _b.message) || error_1.message || 'Unknown error';
                        statusCode = (_c = error_1.response) === null || _c === void 0 ? void 0 : _c.status;
                        logger_1.logger.error("API request failed (attempt ".concat(retryCount + 1, "/").concat(constants_1.RETRY_CONFIG.MAX_RETRIES + 1, "):"), {
                            url: url,
                            status: statusCode,
                            error: errorMessage
                        });
                        if (!(retryCount < constants_1.RETRY_CONFIG.MAX_RETRIES)) return [3 /*break*/, 4];
                        delay_1 = constants_1.RETRY_CONFIG.RETRY_DELAY * Math.pow(constants_1.RETRY_CONFIG.BACKOFF_MULTIPLIER, retryCount);
                        logger_1.logger.info("Retrying in ".concat(delay_1, "ms..."));
                        return [4 /*yield*/, new Promise(function (resolve) { return setTimeout(resolve, delay_1); })];
                    case 3:
                        _d.sent();
                        return [2 /*return*/, this.makeRequestWithRetry(url, params, retryCount + 1)];
                    case 4: throw new Error("API request failed after ".concat(constants_1.RETRY_CONFIG.MAX_RETRIES + 1, " attempts: ").concat(errorMessage));
                    case 5: return [2 /*return*/];
                }
            });
        });
    };
    /**
     * Fetch For You recommendations
     */
    ApiService.prototype.fetchForYouTokens = function () {
        return __awaiter(this, void 0, void 0, function () {
            var url, params;
            return __generator(this, function (_a) {
                url = "".concat(constants_1.API_URLS.FRONTEND_V3, "/coins/for-you");
                params = {
                    offset: constants_1.API_PARAMS.OFFSET,
                    limit: constants_1.API_PARAMS.LIMIT,
                    includeNsfw: constants_1.API_PARAMS.INCLUDE_NSFW
                };
                return [2 /*return*/, this.makeRequestWithRetry(url, params)];
            });
        });
    };
    /**
     * Fetch Featured tokens
     */
    ApiService.prototype.fetchFeaturedTokens = function () {
        return __awaiter(this, void 0, void 0, function () {
            var url, params;
            return __generator(this, function (_a) {
                url = "".concat(constants_1.API_URLS.ADVANCED_V2, "/coins");
                params = {
                    sort_by: 'featured',
                    limit: constants_1.API_PARAMS.LIMIT,
                    offset: constants_1.API_PARAMS.OFFSET
                };
                return [2 /*return*/, this.makeRequestWithRetry(url, params)];
            });
        });
    };
    /**
     * Fetch New tokens
     */
    ApiService.prototype.fetchNewTokens = function () {
        return __awaiter(this, void 0, void 0, function () {
            var url, params;
            return __generator(this, function (_a) {
                url = "".concat(constants_1.API_URLS.ADVANCED_V2, "/coins");
                params = {
                    limit: constants_1.API_PARAMS.LIMIT,
                    offset: constants_1.API_PARAMS.OFFSET
                };
                return [2 /*return*/, this.makeRequestWithRetry(url, params)];
            });
        });
    };
    /**
     * Fetch Graduated tokens
     */
    ApiService.prototype.fetchGraduatedTokens = function () {
        return __awaiter(this, void 0, void 0, function () {
            var url, params;
            return __generator(this, function (_a) {
                url = "".concat(constants_1.API_URLS.ADVANCED_V2, "/coins/graduated");
                params = {
                    limit: constants_1.API_PARAMS.LIMIT,
                    offset: constants_1.API_PARAMS.OFFSET
                };
                return [2 /*return*/, this.makeRequestWithRetry(url, params)];
            });
        });
    };
    /**
     * Fetch Runners tokens
     */
    ApiService.prototype.fetchRunnersTokens = function () {
        return __awaiter(this, void 0, void 0, function () {
            var url;
            return __generator(this, function (_a) {
                url = "".concat(constants_1.API_URLS.LEGACY, "/runners");
                return [2 /*return*/, this.makeRequestWithRetry(url)];
            });
        });
    };
    return ApiService;
}());
exports.ApiService = ApiService;
exports.apiService = new ApiService();
