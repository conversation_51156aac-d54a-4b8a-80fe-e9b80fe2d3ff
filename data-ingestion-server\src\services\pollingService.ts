import * as cron from 'node-cron';
import { apiService } from './apiService';
import { dataProcessor } from './dataProcessor';
import { enrichmentService } from './enrichmentService';
import { normalizeToken, validateToken } from '../utils/normalizer';
import { CATEGORY_CONFIGS } from '../config/constants';
import { ApiType } from '../types/api';
import { logger } from '../utils/logger';

/**
 * Service that manages all polling jobs for different token categories
 */
export class PollingService {
  private jobs: Map<string, cron.ScheduledTask> = new Map();
  private jobStatus: Map<string, boolean> = new Map();

  /**
   * Start all polling jobs
   */
  start(): void {
    logger.info('Starting polling service...');

    CATEGORY_CONFIGS.forEach(config => {
      this.startPollingJob(config.slug, config.pollInterval, config.apiType);
    });

    logger.info(`Started ${CATEGORY_CONFIGS.length} polling jobs`);
  }

  /**
   * Stop all polling jobs
   */
  stop(): void {
    logger.info('Stopping polling service...');

    this.jobs.forEach((job, category) => {
      job.stop();
      logger.info(`Stopped polling job for ${category}`);
    });

    this.jobs.clear();
    this.jobStatus.clear();
    logger.info('All polling jobs stopped');
  }

  /**
   * Start a polling job for a specific category
   */
  private startPollingJob(categorySlug: string, intervalMinutes: number, apiType: ApiType): void {
    const cronExpression = `*/${intervalMinutes} * * * *`; // Every N minutes
    
    const job = cron.schedule(cronExpression, async () => {
      await this.pollCategory(categorySlug, apiType);
    }, {
      scheduled: false // Don't start immediately
    });

    this.jobs.set(categorySlug, job);
    job.start();

    logger.info(`Started polling job for ${categorySlug} (every ${intervalMinutes} minutes)`);

    // Run immediately on startup
    setImmediate(() => {
      this.pollCategory(categorySlug, apiType);
    });
  }

  /**
   * Poll a specific category and process the data
   */
  private async pollCategory(categorySlug: string, apiType: ApiType): Promise<void> {
    try {
      logger.polling(categorySlug, 'Starting poll');

      // Fetch data based on category
      const rawTokens = await this.fetchTokensByCategory(categorySlug);

      if (!rawTokens) {
        logger.polling(categorySlug, 'API returned null/undefined response');
        return;
      }

      if (!Array.isArray(rawTokens)) {
        logger.polling(categorySlug, 'API returned non-array response', { type: typeof rawTokens, data: rawTokens });
        return;
      }

      if (rawTokens.length === 0) {
        logger.polling(categorySlug, 'No tokens returned from API');
        return;
      }

      logger.polling(categorySlug, 'Fetched tokens', { count: rawTokens.length });

      // Normalize tokens
      const normalizedTokens = rawTokens
        .map(token => {
          try {
            return normalizeToken(token, apiType);
          } catch (error) {
            logger.error(`Failed to normalize token in ${categorySlug}:`, error);
            return null;
          }
        })
        .filter((token): token is NonNullable<typeof token> => token !== null)
        .filter(validateToken);

      if (normalizedTokens.length === 0) {
        logger.polling(categorySlug, 'No valid tokens after normalization');
        return;
      }

      logger.polling(categorySlug, 'Normalized tokens', {
        count: normalizedTokens.length,
        filtered: rawTokens.length - normalizedTokens.length
      });

      // Determine if this category should be enriched
      const shouldEnrich = categorySlug === 'graduated' || categorySlug === 'runners';

      // Enrich tokens if needed
      if (shouldEnrich) {
        logger.polling(categorySlug, 'Starting enrichment', { count: normalizedTokens.length });

        const mints = normalizedTokens.map(token => token.mint);
        const enrichmentMap = await enrichmentService.enrichTokens(mints);

        // Apply enrichment data to tokens
        for (const token of normalizedTokens) {
          const enrichmentData = enrichmentMap.get(token.mint);
          if (enrichmentData) {
            // Apply enriched data to token
            token.dexscreenerUrl = enrichmentData.dexscreenerUrl;
            token.fdv = enrichmentData.fdv;
            token.priceNative = enrichmentData.priceNative;
            token.priceUsd = enrichmentData.priceUsd;
            token.priceChangeM5 = enrichmentData.priceChangeM5;
            token.priceChangeH1 = enrichmentData.priceChangeH1;
            token.priceChangeH6 = enrichmentData.priceChangeH6;
            token.priceChangeH24 = enrichmentData.priceChangeH24;
            token.graduationTimestamp = enrichmentData.graduationTimestamp;

            // Store transaction data for market history
            (token as any).enrichmentTxns = {
              txnsM5Buys: enrichmentData.txnsM5Buys,
              txnsM5Sells: enrichmentData.txnsM5Sells,
              txnsH1Buys: enrichmentData.txnsH1Buys,
              txnsH1Sells: enrichmentData.txnsH1Sells,
              txnsH6Buys: enrichmentData.txnsH6Buys,
              txnsH6Sells: enrichmentData.txnsH6Sells,
              txnsH24Buys: enrichmentData.txnsH24Buys,
              txnsH24Sells: enrichmentData.txnsH24Sells,
            };
          }
        }

        logger.polling(categorySlug, 'Enrichment completed', {
          enriched: enrichmentMap.size,
          total: normalizedTokens.length
        });
      }

      // Process and upsert data
      await dataProcessor.processAndUpsertData(normalizedTokens, categorySlug);

      logger.polling(categorySlug, 'Poll completed successfully');

    } catch (error) {
      logger.error(`Polling failed for ${categorySlug}:`, error);
    }
  }

  /**
   * Fetch tokens based on category slug
   */
  private async fetchTokensByCategory(categorySlug: string): Promise<any[]> {
    switch (categorySlug) {
      case 'for-you':
        return apiService.fetchForYouTokens();
      case 'featured':
        return apiService.fetchFeaturedTokens();
      case 'new':
        return apiService.fetchNewTokens();
      case 'graduated':
        return apiService.fetchGraduatedTokens();
      case 'runners':
        return apiService.fetchRunnersTokens();
      default:
        throw new Error(`Unknown category: ${categorySlug}`);
    }
  }

  /**
   * Get status of all polling jobs
   */
  getStatus(): Record<string, boolean> {
    const status: Record<string, boolean> = {};
    
    this.jobs.forEach((job, category) => {
      status[category] = this.jobStatus.get(category) || false;
    });

    return status;
  }

  /**
   * Manually trigger a poll for a specific category
   */
  async triggerPoll(categorySlug: string): Promise<void> {
    const config = CATEGORY_CONFIGS.find(c => c.slug === categorySlug);
    if (!config) {
      throw new Error(`Unknown category: ${categorySlug}`);
    }

    logger.info(`Manually triggering poll for ${categorySlug}`);
    await this.pollCategory(categorySlug, config.apiType);
  }
}

export const pollingService = new PollingService();
